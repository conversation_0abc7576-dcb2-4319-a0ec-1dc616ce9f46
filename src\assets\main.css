@import './base.css';

:root {
  --header-height: 3rem;
}

/* layout */

.layout-container {
  width: 100%;
  padding: 0px 30px;
  background-color: #FAFCFD;

  h2 {
    margin: 20px 0 10px 0;
  }
}

.full-spin {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.full-spin.full-spin-layer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1001;
}
.full-spin .ant-spin-dot{
  font-size: 3rem;
  z-index: 1002;
}
.full-spin.sm .ant-spin-dot {
  font-size: 1.2rem;
}