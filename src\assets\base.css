/* color palette from <https://github.com/vuejs/theme> */
/* https://material-foundation.github.io/material-theme-builder/ */
:root {
  --main-1000: #2c93fb;
  --main-900: #2e93f9;
  --main-800: #389bfe;
  --main-700: #379afc;
  --main-600: #48a1fb;
  --main-500: #4ea1f5;
  --main-400: #5eacfa;
  --main-300: #6cb5fe;
  --main-200: #71b4f8;
  --main-100: #A0CFFF;
  --main-50: #83bef9;
  --main-25: #90c6fc;
  --main-10: #b1d5f8;
  --main-5: #d0e4fa;

  --gray-10000: #000000;
  --gray-2000: #0C1214;
  --gray-1000: #171C1F;
  --gray-900: #212729;
  --gray-800: #42484A;
  --gray-700: #616161;
  --gray-600: #8C9193;
  --gray-500: #A7ACAD;
  --gray-400: #C4C7C8;
  --gray-300: #DFE3E4;
  --gray-200: #EFF1F2;
  --gray-100: #F8FAFB;
  --gray-50: #FBFDFE;
  --gray-10: #FDFEFF;
  --gray-0: #FFFFFF;

  --main-color: #1885f2;
  --main-color-dark: #002BFF;
  --main-light-1: #0076AB;
  --main-light-2: #DAEAED;
  --main-light-3: #EDF0F1;
  --main-light-4: #F2F5F5;
  --main-light-5: #F7FAFB;
  --main-light-6: #FAFDFD;
  --secondry-color: #4e616d;
  --error-color: #ba1a1a;

  --bg-sider: var(--gray-0);
  --color-text: var(--c-black);

  --header-bgcolor-4: #E1E9FE;

  --min-width: 400px;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  position: relative;
}
html {
  font-size: 16px;
}
body {
  display: flow-root;
  min-height: 100vh;
  color: var(--gray-900);
  line-height: 2rem;
  font-family: 'Roboto', 'Noto Sans SC', 'HarmonyOS Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
