<template>
  <div class="welcome">
    <div class="tip">{{ messageTip }}</div>
    <Spin class="full-spin full-spin-layer" v-if="loading"></Spin>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Spin } from 'ant-design-vue';
import api from '@/config/api.js'
import {request} from '@/hooks/utils.js'
import { useMessage } from '@/hooks/useChat.js'

const router = useRouter();
const messageTip = ref("加载中...")
const loading = ref(true)

const goToChat = (params) => {
  if(params.third === '1' && params.type === 'detail') {
    router.push({
      name: 'ChatComp',
      query: params
    });
    return;
  }
  router.push({
    name: params.busType === '2' ? 'YunImageChat' : 'ChatComp',
    query: params
  });
}

// 解析路由参数登录
const parseQuery = async () => {
  const query = router.currentRoute.value.query;
  // 第三方进入
  if(query.third === '1') {
    if(query.type === '' && !query.chatId) {
      messageTip.value = "对话id不能为空";
      message.error(messageTip.value);
      loading.value = false;
      return;
    }
    sessionStorage.setItem('ai_general_info', JSON.stringify(query));
    goToChat({
      third: query.third,
      chatId: query.chatId,
    })
    return;
  }
  if(!query.paramStr) {
    messageTip.value = "paramStr参数错误";
    message.error(messageTip.value);
    loading.value = false;
    return;
  }
  loading.value = true;
  let params = {
    paramStr: query.paramStr
  }
  let res = await request(api.srLogin, 'post', params);
  if (!res || res.status !== '0') {
    messageTip.value = res?.message || '操作失败';
    message.error(messageTip.value);
    loading.value = false;
    return;
  }
  let data = res.result || {};
  /**
   * busType业务类型
   * 1：睿图医生助手
   * 2：云胶片报告-解读
   * 3：病理AI助手
   * 9：需求工单生成
   */
  let {userId, examNo = '', busId = '', busType = ''} = data;
  if(!userId) {
    messageTip.value = "登录失败";
    message.error(messageTip.value);
    loading.value = false;
    return;
  }
  if(!['1', '2', '3', '9'].includes(busType)) {
    messageTip.value = "业务类型错误";
    message.error(messageTip.value);
    loading.value = false;
    return;
  }
  if(!busId && examNo) {
    busId = examNo;
    data.busId = busId;
  }
  // loading.value = false;
  if(userId) {
    goToChat({
      userId,
      busId,
      busType
    })
  }
  sessionStorage.setItem('ai_general_info', JSON.stringify(data));
}

onMounted(() => {
  parseQuery();
})
</script>


<style lang="less" scoped>
.welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  color: #333;
  text-align: center;
  background: #F1F1F1;
  .tip {
    font-size: 2rem;
    color: #000;
    margin-top: 10rem;
  }
}
</style>
