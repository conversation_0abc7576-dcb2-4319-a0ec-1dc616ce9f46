<template>
  <a-modal 
    v-model:open="open" 
    class="key-word-setting"
    title="常用提示词管理"
    cancelText="取消"
    okText="保存"
    style="width: fit-content;max-width: 100%;"
    @ok="saveCueWordHandler"
  >
    <div class="setting-wrap">
      <div class="left-wrap">
        <Spin class="full-spin full-spin-layer" v-if="loading"></Spin>
        <div class="cate-wrap">
          <div class="tab-wrap">
            <div :class="{'tab-item': true, 'active': curPrivateFlag === '0'}" @click="tabClick('0')">公有</div>
            <div :class="{'tab-item': true, 'active': curPrivateFlag === '1'}" @click="tabClick('1')">私有</div>
          </div>
          <div class="tab-content" ref="wordRef" v-if="cueWordList?.length">
            <div 
              :class="{'cate-item': true, 'active': editOrAdd === 'edit' && item.paramName === curCueWordInfo.title}" 
              v-for="(item, index) in cueWordList" 
              :key="index"
            >
              <div class="cate-title" @click="selectWordHandler(item)">{{item.paramName || ''}}</div>
              <a-popover placement="bottom" :trigger="['click']">
                <template #content>
                  <div>
                    <Popconfirm placement="top" ok-text="确定" cancel-text="取消" @confirm.stop="delCueWord(item)">
                      <template #title>
                        <p>确定要删除该常用词？</p>
                      </template>
                      <div class="cate-popover-opt-btn">
                        <DeleteOutlined :style="{color:'#ff4d4f'}" />
                        <span style="margin-left: 0.5rem;">删除</span>
                      </div>
                    </Popconfirm>
                  </div>
                </template>
                <EllipsisOutlined />
              </a-popover>
            </div>
          </div>
          <div class="nodata-wrap" v-else>暂无数据</div>
        </div>
        <div class="add-wrap">
          <a-button class="add-btn" type="primary" ghost @click="addWordHandler">
            <PlusOutlined />
            <span>新增提示词</span>
          </a-button>
        </div>
      </div>
      <div class="right-wrap">
        <div class="key-title">
          <span class="r-label">提示词标题：</span>
          <a-input v-model:value="curCueWordInfo.title" placeholder="请输入" :readonly="editOrAdd === 'edit'"/>
        </div>
        <div class="key-cont">
          <div class="r-label">提示词正文：</div>
          <a-textarea
            v-model:value="curCueWordInfo.content"
            placeholder="请输入正文"
            :rows="15"
          />
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, toRefs, watch, nextTick } from 'vue';
import { message, Popconfirm, Spin } from 'ant-design-vue'
import { getCueWordList, saveCueWord, deleteCueWord } from '@/hooks/useChat.js'
import { PlusOutlined, EllipsisOutlined, DeleteOutlined } from '@ant-design/icons-vue';
const emit = defineEmits(['update:showDialog', 'close']);
const props = defineProps({
  state: Object,
  showDialog: {
    type: Boolean,
    default: false
  },
  privateFlag: {
    type: String,
    default: '1'
  },
});

let {privateFlag} = toRefs(props);

const wordRef = ref(null);
const open = ref(false);
const curCueWordInfo = ref({});
const cueWordList = ref([]);
const curPrivateFlag = ref('1');
const loading = ref(false);

const editOrAdd = ref('add');  //新增/编辑

watch(
  () => privateFlag.value,
  (newValue) => {
    curPrivateFlag.value = newValue;
  }
);

// 删除常用词
const delCueWord = async (item) => {
  let params = {
    userId: props.state?.userInfo?.userId,
    privateFlag: curPrivateFlag.value,   // 1：私有 0：公有
    title: item.paramName,
  };
  let res = await deleteCueWord(params);
  if(res) {
    getCueWordListHandler();
    // 删除当前选中的提示词，置空
    if(item.paramName === curCueWordInfo.value.title) {
      addWordHandler();
    }
  }
}

// 切换tab
const tabClick = (type) => {
  if(type === curPrivateFlag.value) {
    return;
  }
  curPrivateFlag.value = type;
  addWordHandler();
  getCueWordListHandler();
}

// 获取常用词列表
const getCueWordListHandler = async () => {
  cueWordList.value = [];
  let params = {
    userId: props.state?.userInfo?.userId,
    privateFlag: curPrivateFlag.value,   // 1：私有 0：公有
  };
  loading.value = true;
  let res = await getCueWordList(params);
  cueWordList.value = res || [];
  nextTick(() => {
    loading.value = false;
    if(cueWordList.value?.length > 0) {
      wordRef.value.scrollTop = document.querySelector('.cate-item.active')?.offsetTop;
    }
  })
}

// 选中提示词
const selectWordHandler = (item) => {
  curCueWordInfo.value.title = item.paramName || '';
  curCueWordInfo.value.content = item.paramValue || '';
  editOrAdd.value = 'edit';
}

// 添加提示词
const addWordHandler = () => {
  editOrAdd.value = 'add';
  curCueWordInfo.value.title = '';
  curCueWordInfo.value.content = '';
}

// 保存常用词
const saveCueWordHandler = async () => {
  if(!curCueWordInfo.value.title) {
    message.error('请输入提示词标题');
    return;
  }
  if(!curCueWordInfo.value.content) {
    message.error('请输入提示词正文');
    return;
  }
  let params = {
    userId: props.state?.userInfo?.userId,
    privateFlag: curPrivateFlag.value,   // 1：私有 0：公有
    title: curCueWordInfo.value.title,
    query: curCueWordInfo.value.content,
    updateFlag: editOrAdd.value === 'add' ? '' : '1',
  };
  let res = await saveCueWord(params);
  if(res) {
    editOrAdd.value = 'edit';
    getCueWordListHandler();
  }
}


watch(() => props.showDialog, (newVal) => {
  open.value = newVal;
  if(newVal) {
    getCueWordListHandler();
  } else {
    curCueWordInfo.value = {};
    editOrAdd.value = 'add';
    emit('close', curPrivateFlag.value);
  }
})
watch(() => open.value, (newVal) => {
  emit('update:showDialog', newVal);
})
</script>

<style lang="less">
.key-word-setting {
  .ant-modal-close{
    right: 5px;
  }
  .setting-wrap {
    display: flex;
    background: rgba(255,255,255,0);
    border-radius: 0.5rem 0rem 0rem 0.5rem;
    border: 1px solid #DCDFE6;
    height: 28rem;
    .left-wrap {
      width: 16.75rem;
      height: 100%;
      border-right: 1px solid #DCDFE6;
      padding: 0 1rem 1rem 1rem;
      .cate-wrap {
        height: calc(100% - 2.25rem);
        .tab-wrap {
          display: flex;
          .tab-item {
            flex: 1;
            height: 3rem;
            line-height: 3rem;
            text-align: center;
            border-bottom: 1px solid #DCDFE6;
            font-size: 1rem;
            cursor: pointer;
            &:hover {
              opacity: 0.8;
            }
            &.active {
              border-bottom: 2px solid #1885F2;
              color: #1885F2;
              font-weight: bold;
            }
          }
        }
        .tab-content {
          height: calc(100% - 3rem);
          padding: 0.75rem 0;
          overflow: auto;
          .cate-item {
            padding: 0.56rem 0.75rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            &:hover {
              opacity: 0.8;
            }
            &.active {
              background: #ECF5FF;
              .cate-title {
                color: #1885F2;
              }
            }
            .cate-title {
              flex: 1;
              margin-right: 0.5rem;
              color: #303133;
            }
          }
        }
        .nodata-wrap {
          margin-top: 0.5rem;
          color: #666;
        }
      }
      .add-wrap {
        height: 2.25rem;
        .add-btn {
          width: 100%;
        }
      }
    }
    .right-wrap {
      width: 33rem;
      height: 100%;
      padding: 1rem;
      .r-label {
        font-size: 0.88rem;
        color: #606266;
      }
      .key-title {
        display: flex;
        margin-bottom: 0.75rem;
        align-items: center;
        .ant-input {
          flex: 1;
        }
      }
      .key-cont {
        .ant-input {
          resize: none;
        }
      }
    }
  }
}
.cate-popover-opt-btn {
  cursor: pointer;
}
</style>
