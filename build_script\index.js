const fs = require('fs');
const path = require('path');
const children_process = require('child_process');
const { fileName, fileMap, SHELL } = require('./const');
const targetPath = path.resolve(process.cwd(), fileName);
const data = Object.entries(fileMap)
  .map(([key, value]) => {
    return `${key}="${value}"`;
  })
  .join('\n');
fs.writeFile(targetPath, data, 'utf-8', (err, data) => {
  if (!err) {
    children_process.spawnSync(SHELL, { stdio: 'inherit', shell: true });
  }
});
