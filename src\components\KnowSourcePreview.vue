<template>
  <a-modal 
    v-model:open="open" 
    :footer="null"
    class="know-source-preview"
    style="width: fit-content;max-width: 100%;"
  >
    <!-- :mask="false" -->
    <template #title>
      <div class="doc-title">
        <img :src="docIconMap[currentDoc.doc_ext]" alt="" v-if="docIconMap[currentDoc.doc_ext]">
        <span class="doc-name" @click="toDocPage(currentDoc)">{{currentDoc.resourceTitle || ''}}</span>
      </div>
    </template>
    <div class="tip">此处仅显示实际引用内容，若数据有更新，此处不会实时更新</div>
    <div class="content know-message-md">
      <!-- <iframe :src="docUrl" frameborder="0"></iframe> -->
      <component :is="renderedNote" v-if="renderedNote"></component>
    </div>
  </a-modal>
  <KnowArticlePreview
    :showDialog.sync="showArticleDialog"
    :docUrl="docUrl"
    @update:showDialog="(newVal) => showArticleDialog = newVal"
  ></KnowArticlePreview>
</template>

<script setup>
import { ref, toRefs, watch } from 'vue';
import { message } from 'ant-design-vue'
import {request} from '@/hooks/utils.js';
import api from '@/config/api.js';
import {docIconMap} from '@/config/constant.js'
import { useMarkChatContent, getDocUrl } from '@/hooks/useChat.js'
import KnowArticlePreview from '@/components/KnowArticlePreview.vue'
const {bubbleRenderMessage} = useMarkChatContent();
const emit = defineEmits(['update:showDialog']);
const props = defineProps({
  from: {
    type: String,
    default: ''
  },
  showDialog: {
    type: Boolean,
    default: false
  },
  currentDoc: {
    type: Object,
    default: () => {}
  },
});

let {currentDoc} = toRefs(props);
let renderedNote = null;

const open = ref(false);
const docUrl = ref('');
const showArticleDialog = ref(false);  //查看文章

// 跳转至文档详情页
const toDocPage = async () => {
  let params = {
    docId: props.currentDoc.resourceId
  };
  let url = await getDocUrl(params);
  if(!url) {
    return;
  }
  showArticleDialog.value = true;
  docUrl.value = url;
  open.value = false;
  // window.open(url);
}

watch(() => props.showDialog, (newVal) => {
  open.value = newVal;
  if(newVal) {
    let docName = props.currentDoc?.resourceTitle;
    let docNameInfo = docName?.split('.') || [];
    props.currentDoc.doc_ext = docNameInfo[docNameInfo.length - 1] || '';
    renderedNote = bubbleRenderMessage(currentDoc.value.content || '');
  }
})
watch(() => open.value, (newVal) => {
  emit('update:showDialog', newVal);
})
</script>

<style lang="less">
.know-source-preview {
  .ant-modal-close{
    right: 5px;
  }
  .doc-title {
    display: flex;
    align-items: center;
    img {
      width: 1.5rem;
      height: 1.5rem;
      margin-right: 0.5rem;
    }
    .doc-name {
      font-weight: bold;
      font-size: 1.13rem;
      color: #1885F2;
      text-decoration-line: underline;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
    .normal-name {
      font-weight: bold;
      font-size: 1.13rem;
      color: #303133;
    }
  }
  .tip {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    color: #303133;
  }
  .content {
    width: 100%;
    max-height: 36rem; 
    overflow: auto;
    border-radius: 0.44rem;
    border: 1px solid #DCDFE6;
    iframe {
      width: 100%;
      height: 100%;
    }
  }
}
.know-message-md {
  word-wrap: break-word;
  // white-space: pre-line;
  padding: 0.5rem;
  .ant-typography {
    font-size: 1rem;
    line-height: 1.5;
  }
  .note {
    display: inline-block;
    color: #002BFF;
    cursor: pointer;
    margin-right: 0.25rem;
    &:hover {
      opacity: 0.8;
    }
    &:before {
      content: '['
    }
    &:after {
      content: ']'
    }
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: 1rem;
  }
  p {
    margin: 0;
  }
  li, ol, ul {
    margin: 0;
    padding-top: 0;
    padding-bottom: 0;
    & > p {
      margin: 0.25rem 0;
    }
  }
  table {
    border-collapse: collapse;
    th, td {
      border: 1px solid #9a9a9a;
      padding: 4px;
    }
  }
  h1,h2,h3,h4,h5 {
    margin-top: 0!important;
    margin-bottom: 0!important;
  }
}
// @media (max-width: 520px) {
//   .know-source-preview {
//     max-width: 100% !important;
//   }
// }
@media (min-width: 900px) {
  .know-source-preview {
    max-width: 60% !important;
  }
}
</style>
