<template>
  <div :class="{'chat':true, 'no-head': thirdType === 'detail'}">
    <Spin class="full-spin full-spin-layer" v-if="isLoadMsg && !isStreaming.includes(conv?.id)"></Spin>
    <div class="header" v-if="!isThird">
      <div class="header__left">
        <template v-if="!showMoreAiTool">
          <div
            v-if="!state.isSidebarOpen"
            class="close nav-btn"
            @click="state.isSidebarOpen = true; state.miniSideBarOpen = true"
          >
            <MenuOutlined />
          </div>
          <a-tooltip title="历史对话" placement="left">
            <div class="his nav-btn" @click="state.isSidebarOpen = true; state.miniSideBarOpen = true">
              <HistoryOutlined />
            </div>
          </a-tooltip>
        </template>
      </div>
      <div class="header__center" v-if="conv && !showMoreAiTool" :title="conv.title">{{ conv.title }}</div>
      <div class="header__center" v-if="showMoreAiTool">更多AI助手</div>
      <div class="header__right">
        <a-tooltip title="睿图AI助手" placement="left" v-if="showMoreAiTool || currentAiTool">
          <div class="newchat nav-btn" @click="handleClickTool('')">
            <img src="@/assets/images/icons/robot-icon.png" alt="">
          </div>
        </a-tooltip>
        <a-tooltip title="更多" placement="left" v-if="!showMoreAiTool && !currentAiTool">
          <div class="newchat nav-btn" @click="toggleAiTool(true)">
            <img src="@/assets/images/icons/more-icon.png" alt="">
          </div>
        </a-tooltip>
        <a-tooltip title="新建对话" placement="left">
          <div class="newchat nav-btn" @click="$emit('newconv', '')">
            <img src="@/assets/images/icons/new-icon.png" alt="">
          </div>
        </a-tooltip>
      </div>
    </div>
    <div class="chat-box" ref="chatContainer" @scroll="handleScroll" v-if="!showMoreAiTool">
      <div class="more-box" title="加载更多" v-if="isLoadMoreMsg">
        <Spin class="full-spin sm"></Spin>
      </div>
      <!-- 提示框 -->
      <div class="message-box received" v-if="!hasMoreFlag && thirdType !== 'detail'">
        <div class="role-avatar">
          <img src="@/assets/images/ai-avatar.png" alt="">
        </div>
        <!-- v-if="conv?.id?.endsWith('_exams')" -->
        <div class="message-md received-box" v-if="conv?.examNo || currentAiTool">
          <div class="received-tip">
            {{ currentAiTool ? '你好，我是睿图知识库查询助手，我已对接睿图知识库，你可以问我任何问题，我将为你进行智能查询。' : '你好，我是睿图AI助手，我将为你提供智能协助诊断。你可以问我任何问题。' }}
          </div>
          <div class="received-qus" v-if="!currentAiTool">猜你想问：</div>
          <div class="received-example" v-if="!currentAiTool">
            <span 
              :class="{'example': true, 'disabled': isStreaming.includes(conv.id)}" 
              v-for="item in exampleList"
              @click="autoSend(item.example)"
            >{{item.example}}</span>
          </div>
        </div>
        <div class="message-md received-box" v-else>
          <div class="received-tip">你好，我是小睿，我将为你提供智能协助诊断。你可以问我任何问题。</div>
        </div>
      </div>
      <div
        v-for="(message, i) in conv?.messages"
        :key="message.id"
        class="message-box"
        :class="message.role"
        :ref="i === conv.messages.length - 1 ? 'messageBox' : ''"
      >
        <div class="role-avatar" v-if="message.role !== 'sent'">
          <img src="@/assets/images/ai-avatar.png" alt="">
        </div>
        <div v-if="message.reasoning_content" class="searching-msg">
          <a-collapse
            v-model:activeKey="message.showThinking"
            :bordered="false"
            style="background: rgb(255, 255, 255)"
            @change="toggleCollapse"
          >
            <template #expandIcon="{ isActive }">
              <caret-right-outlined :rotate="isActive ? 90 : 0" />
            </template>
            <a-collapse-panel
              key="show"
              :header="message.status=='reasoning' ? '正在思考...' : '推理过程'"
              :style="'background: #f7f7f7; border-radius: 8px; margin-bottom: 24px; border: 0; overflow: hidden'"
            >
            <Bubble
              :content="message.reasoning_content"
              :typing="!!message.splitByLetter && !message.noShowThinkBubble ? aiBubbleType : false"
            ></Bubble>
              <!-- <p style="white-space: pre-line;word-break: break-word;">{{ message.reasoning_content }}</p> -->
            </a-collapse-panel>
          </a-collapse>
        </div>
        <p v-if="message.role=='sent'" style="white-space: pre-line" class="message-text">{{ message.text }}</p>
        <RefsComponent 
          v-if="message.role=='sent' && message.text"
          class="refs-com"
          :message="message" 
          @delete-conv="deleteConvHandler"
        />
        <!-- <div v-if="message.role=='sent'"> -->
        <!-- </div> -->
        <div v-else-if="message.status === 'customError'">
          {{ message.text }}
        </div>
        <div v-else-if="message.text?.length == 0 && message.status=='init'"  class="loading-dots">
          <div></div>
          <div></div>
          <div></div>
        </div>
        <div v-else-if="message.status == 'searching' && isStreaming.includes(conv?.id)" class="searching-msg"><i>正在检索……</i></div>
        <div v-else-if="message.status === 'generating' && isStreaming.includes(conv?.id)" class="searching-msg"><i>正在生成……</i></div>
        <div
          v-else-if="(message.text?.length == 0 && !['reasoning', 'pausing', 'finished', 'loading'].includes(message.status)) || message.status == 'error' || (!['finished', 'pausing', 'loading'].includes(message.status) && !isStreaming.includes(conv?.id))"
          class="err-msg"
          @click="retryMessage(message.id)"
        >
          请求错误，请重试。
        </div>
        <div v-else
          class="message-md"
          @click="clickNoteHandler($event, message)"
          :key="message.id"
        >
          <Bubble
            :content="message.text"
            :messageRender="bubbleRenderMessage"
            :typing="!!message.splitByLetter ? aiBubbleType : false"
            :onTypingComplete="finishReasponHandler(message)"
          >
          </Bubble>
        </div>
        <KnowSourceWrap 
          v-if="message.role=='received' && message.status=='finished' && (!message.splitByLetter || message.isBubbleFlag)"
          :docInfoList="message.chatRefList"
        ></KnowSourceWrap>
        <RefsComponent 
          v-if="message.role=='received' && message.status=='finished' && (!message.splitByLetter || message.isBubbleFlag)" 
          :message="message" 
          :showPosition="message.question === '报告纠错' && state.userInfo?.busType === '1' && i === conv.messages.length - 1"
          @delete-conv="deleteConvHandler"
          @return-mark="returnMarkHandler"
        />
      </div>
    </div>
    <!-- AI工具助手 -->
    <div class="tools-wrap" v-else>
      <div class="tools-item" 
        v-for="(item, i) in constant.aiToolList" 
        :key="item.type"
        @click="handleClickTool(item.type)"
      >
        <img :src="item.icon" v-if="item.icon">
        <span>{{item.name}}</span>
      </div>
    </div>
    <div class="bottom" v-if="!showMoreAiTool && thirdType !== 'detail'">
      <div class="example-bottom">
        <template v-if="conv?.examNo && !currentAiTool">
          <span 
            :class="{'example-card': true, 'disabled': isStreaming.includes(conv.id)}" 
            v-for="item in exampleList"
            @click="autoSend(item.example)"
          >
            <img :src="item.icon" alt="" class="example-img">{{item.example}}
          </span>
        </template>
        <CueWordButton 
          :disabled="isStreaming.includes(conv?.id)"
          :state="state"
          @quoteKeyWord="quoteKeyWordHandler"
        ></CueWordButton>
      </div>
      <div :class="{'input-box': true, 'ai-box': currentAiTool}" v-if="conv && thirdType !== 'detail'">
        <div class="input-wrap">
          <div class="input-area">
            <a-textarea
              :readonly="isStreaming.includes(conv?.id)"
              class="user-input"
              v-model:value="conv.inputText"
              @keydown="handleKeyDown"
              placeholder="你可以问我任何问题～"
              :autoSize="{ minRows: 2, maxRows: 3 }"
            />
          </div>
          <!-- <a-button size="large" @click="sendMessage()" :disabled="(!conv.inputText && !isStreaming)" type="link">
            <template #icon> <ArrowUpOutlined v-if="!isStreaming" /> <LoadingOutlined v-else/> </template>
          </a-button> -->
          <a-button size="large" @click="sendMessage" :disabled="!conv.inputText" type="link" v-if="!isStreaming.includes(conv.id)">
            <template #icon> <ArrowUpOutlined v-if="!isStreaming.includes(conv.id)" /> <LoadingOutlined v-else/> </template>
          </a-button>
          <a-button title="停止" size="large" @click="pauseFetchHandler()" type="link" v-if="!pauseStreaming.includes(conv.id) && isStreaming.includes(conv.id)">
            <template #icon> <PauseOutlined /> </template>
          </a-button>
        </div>
        <div class="ai-tool-wrap" v-if="currentAiTool">
          <KnowCaseAiDialog
            :state="state"
            :aiCaseInfo="aiCaseInfo"
            @changeCase="changeCaseHandler"
          ></KnowCaseAiDialog>
        </div>
      </div>
    </div>
    <KnowSourcePreview
      from="note"
      :showDialog.sync="showNoteDialog"
      :currentDoc="currentNote"
      @update:showDialog="(newVal) => showNoteDialog = newVal"
      ></KnowSourcePreview>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, toRefs, nextTick, defineExpose, watch, onBeforeUnmount } from 'vue'
import {
  MenuOutlined,
  LoadingOutlined,
  ArrowUpOutlined,
  PauseOutlined,
  MessageOutlined,
  HistoryOutlined,
  CaretRightOutlined,
} from '@ant-design/icons-vue'
import { Bubble } from 'ant-design-x-vue';
import { onClickOutside } from '@vueuse/core'
import { message, Spin } from 'ant-design-vue'
import RefsComponent from '@/components/RefsComponent.vue'
import rptIcon from '@/assets/images/icons/rpt-icon.png'
import timeIcon from '@/assets/images/icons/time-icon.png'
import diagIcon from '@/assets/images/icons/diag-icon.png'
import api from '@/config/api.js'
import * as constant from '@/config/constant.js'
import {generateRandomHash} from '@/hooks/utils.js'
import { useMarkChatContent, useMessage, getNoteContent } from '@/hooks/useChat.js'
import KnowCaseAiDialog from '@/components/KnowCaseAiDialog.vue'
import KnowSourceWrap from '@/components/KnowSourceWrap.vue'
import KnowSourcePreview from '@/components/KnowSourcePreview.vue'
import CueWordButton from '@/components/CueWordButton.vue'

const {bubbleRenderMessage, aiBubbleType, heightObserver, toggleCollapse, resizeObserver} = useMarkChatContent();
const {findCurrentMessage, scrollToBottom, groupRefs, updateMessage} = useMessage();

const props = defineProps({
  allConvs: Array,
  conv: Object,
  state: Object,
  isLoadMoreMsg: Boolean,
  isLoadMsg: Boolean,
  hasMoreFlag: Boolean,
  scrollToBottomFlag: Boolean,
  pauseFlag: Boolean,
})

const emit = defineEmits(['rename-title', 'newconv', 'chat-scroll', 'reopen-ai']);

const { allConvs, conv, state, isLoadMoreMsg, isLoadMsg, hasMoreFlag, scrollToBottomFlag, pauseFlag } = toRefs(props)
const chatContainer = ref(null)
const messageBox = ref(null)
const isStreaming = ref([])
const panel = ref(null)
const modelCard = ref(null)
const pauseStreaming = ref([])
let controllerMap = {};
let showMoreAiTool = ref(false);  // 是否显示AI知识库
let currentAiTool = ref('');   //当前使用的AI工具
let aiCaseInfo = ref({}); //AI助手使用的知识库信息
let showNoteDialog = ref(false);  // 是否显示AI知识库
let currentNote = ref({});
/**
 * 来自向外部的请求信息，和倒计时状态【以examNo】作为键值
 * infoFromOuterSys.examInfoFromOuter  获取到的检查信息
 * infoFromOuterSys.getExamInfoFlag  是否在等待接收外部返回的检查信息
 * infoFromOuterSys.resetFlagTimer  3秒倒计时状态
 * infoFromOuterSys.sendFlag  是否已发送报告信息，否的时候检查号不对应不发送，否则发送一条新对话；是的话不用处理后台已有数据返回
 */
const infoFromOuterSys = ref({});  
let curExamNo = state.value?.userInfo?.busId;
const isThird = ref(state.value?.userInfo?.third === '1');
const thirdType = ref(state.value?.userInfo?.type);

const exampleList = reactive([
  { example: '报告纠错', description: '', icon: rptIcon },
  { example: '病历总结', description: '', icon: timeIcon },
  { example: '鉴别诊断', description: '', icon: diagIcon },
])

const opts = reactive({
  showPanel: false,
  showModelCard: false,
  openDetail: false,
  databases: [],
})

const meta = reactive(JSON.parse(localStorage.getItem('meta')) || {
  use_graph: false,
  use_web: false,
  graph_name: "neo4j",
  selectedKB: null,
  stream: true,
  summary_title: false,
  history_round: 5,
  db_name: null,
})

const consoleMsg = (msg) => console.log(msg)
onClickOutside(panel, () => setTimeout(() => opts.showPanel = false, 30))
onClickOutside(modelCard, () => setTimeout(() => opts.showModelCard = false, 30))

// 接收pacs传入的参数，执行相关动作
window.srCommonOuterOptHandler = (params, callBack) => {
  let {type, result = {}, status, reqType} = params || {};
  const currentExamNo = result?.examNo || '';
  if(type && typeof type !== 'string') {
    type = String(type);
  }
  // 7:AI助手信息回调
  if(!['7', '8'].includes(type) || !constant.aiTypeAndName[type]) {
    console.error('srCommonOuterOpt类型不存在-->' + type);
    return;
  }
  // 切换检查
  if(type === '8') {
    changePatientHandler(result);
  } else if(type == '7') {
    if(!getInfoFromOuterSysValue(currentExamNo, 'getExamInfoFlag')) {
      return;
    }
    clearTimeFromPacs(currentExamNo);
    setInfoFromOuterSysValue(currentExamNo, 'getExamInfoFlag', false);
    if(currentExamNo) {
      let {examNo = '', name = '', patLocalId = ''} = result || {};
      // if(examNo !== curExamNo) {
      if(!getInfoFromOuterSysValue(examNo)) {
        appendUserMessage(constant.aiTypeAndName[type], examNo);
        appendAiMessage(
          `抱歉，当前对话（${name} ${patLocalId}）与当前所打开的报告（${state.value?.userInfo?.name || ''} ${state.value?.userInfo?.patLocalId || ''}）不是同一个检查，无法为您进行报告纠错，请切换后重试！`,
          null,
          'customError',
          examNo
        );
        toggleStreaming(false, '', examNo);
        return;
      }
      autoSend(constant.aiTypeAndName[type], result || {});
    } else {
      // appendUserMessage(constant.aiTypeAndName[type]);
      // appendAiMessage(
      //   `找不到相关检查信息`,
      //   null,
      //   'customError'
      // );
    }
  }
  if(callBack) {
    callBack();
  }
}

const handleKeyDown = (e) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    sendMessage()
  } else if (e.key === 'Enter' && e.shiftKey) {
    // Insert a newline character at the current cursor position
    const textarea = e.target;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    conv.value.inputText.value =
      conv.value.inputText.value.substring(0, start) +
      '\n' +
      conv.value.inputText.value.substring(end);
    nextTick(() => {
      textarea.setSelectionRange(start + 1, start + 1);
    });
  }
}

const renameTitle = () => {
  if(conv.value.title === '新对话') {
    emit('rename-title', conv.value.messages[0].text);
  }
}


const appendUserMessage = (msg, examNo) => {
  let allConvList = allConvs.value;
  let curConv = !examNo ? conv.value : allConvList.find(item => item.examNo === examNo);
  curConv?.messages?.push({
    id: generateRandomHash(16),
    role: 'sent',
    text: msg,
    createTime: Date.now()
  })
  scrollToBottom(chatContainer)
}

const appendAiMessage = (text, refs=null, status, examNo) => {
  let allConvList = allConvs.value;
  let curConv = !examNo ? conv.value : allConvList.find(item => item.examNo === examNo);
  curConv?.messages?.push({
    id: generateRandomHash(16),
    role: 'received',
    text: text,
    reasoning_content: '',
    refs,
    status: status || "init",
    meta: {},
    showThinking: "show"
  })
  scrollToBottom(chatContainer)
}

const loadDatabases = () => {
  fetch('/api/data/', { method: "GET", })
    .then(response => response.json())
    .then(data => {
      console.log(data)
      opts.databases = data.databases
    })
}

// 新函数用于处理 fetch 请求
const fetchChatResponse = async(user_input, cur_res_id, convId) => {
  let curConvId = convId || conv.value.id;
  const controller = new AbortController();
  controllerMap[curConvId] = {
    controller,
    signal: controller.signal
  }
  const sendExamNo = allConvs.value.find(item => item.id === curConvId)?.examNo || '';
  togglePauseStreaming(false, curConvId);
  let params = {
    query: user_input,
    history: conv.value.history,
    cur_res_id: curConvId,
    userId: state.value?.userInfo?.userId,
    busType: currentAiTool.value || state.value?.userInfo?.busType,
    examNo: sendExamNo,
  };
  // 知识库查询
  if(currentAiTool.value === '5') {
    params.dataSetList = aiCaseInfo.value?.checkedCaseIds;
  }
  if(getInfoFromOuterSysValue(sendExamNo, 'examInfoFromOuter')) {
    let {description, impression, sampleSeen, examParam, recommendation} = getInfoFromOuterSysValue(sendExamNo, 'examInfoFromOuter');
    params.description = description;
    params.impression = impression;
    params.sampleSeen = sampleSeen;
    params.examParam = examParam;
    params.recommendation = recommendation;
  }
  setInfoFromOuterSysValue(sendExamNo, null);
  startObserveHeightToScroll();
  fetch(api.sendQuestion, {
    signal: controllerMap[curConvId].signal,
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then((response) => {
    if (!response.body) throw new Error("ReadableStream not supported.");
    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = '';

    const readChunk = () => {
      return reader.read().then(({ done, value }) => {
        if (done || pauseStreaming?.value?.includes(curConvId)) {
          console.log('手动停止：', pauseStreaming?.value?.includes(curConvId));
          groupRefs(cur_res_id, allConvs.value, chatContainer);
          updateMessage({showThinking: "no", id: cur_res_id}, conv, allConvs.value, chatContainer);
          toggleStreaming(false, curConvId);
          if (conv.value?.messages?.length === 2) { renameTitle(); }
          return;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');

        // 处理除最后一行外的所有完整行
        for (let i = 0; i < lines.length - 1; i++) {
          const line = lines[i].trim();
          if (line) {
            try {
              const data = JSON.parse(line);
              updateMessage({
                id: cur_res_id,
                text: data.response,
                reasoning_content: data.reasoning_content,
                status: data.status,
                meta: data.meta,
                ...data,
                splitByLetter: true
              }, conv, allConvs.value, chatContainer);
              // console.log("Last message", conv.value.messages[conv.value.messages.length - 1].text)
              // console.log("Last message", conv.value.messages[conv.value.messages.length - 1].status)

              if (data.history) {
                conv.value.history = data.history;
              }
            } catch (e) {
              console.error('JSON 解析错误:', e, line);
            }
          }
        }

        // 保留最后一个可能不完整的行
        buffer = lines[lines.length - 1];

        return readChunk(); // 继续读取
      }).catch((error) => {
        console.error('读取错误：', error);
      });
    };
    readChunk();
  })
  .catch((error) => {
    if(error === '取消请求') {
      console.log('取消请求：',error);
    } else {
      console.error('请求报错：', error);
      updateMessage({
        id: cur_res_id,
        status: "error",
      }, conv, allConvs.value, chatContainer);
    }
    toggleStreaming(false, curConvId);
  });
}

// 停止请求
const pauseFetchHandler = () => {
  const cur_res_id = conv.value.messages[conv.value.messages.length - 1]?.id;
  if(controllerMap[conv.value.id]?.controller?.abort) {
    controllerMap[conv.value.id].controller.abort('取消请求');
    togglePauseStreaming(true, conv.value.id);
    toggleStreaming(false, conv.value.id);
    if(cur_res_id) {
      // const msg = conv.value.messages.find((msg) => msg.id === cur_res_id)
      const {msg, convId} = findCurrentMessage(cur_res_id, allConvs.value);
      if(msg.status === 'loading' || msg.status === 'reasoning') {
        updateMessage({
          id: cur_res_id,
          status: msg.text.length ? "finished" : "pausing",
          splitByLetter: false
        }, conv, allConvs.value, chatContainer);
      }
    }
  } else {
    togglePauseStreaming(true, conv.value.id);
    toggleStreaming(false, conv.value.id);
  }
}

// 开始监听高度变化
const startObserveHeightToScroll = () => {
  nextTick(() => {
    if(messageBox.value) {
      heightObserver(messageBox.value[0], 0, () => {
        scrollToBottom(chatContainer);
      });
    }
  })
}

// 切换发送对话中的状态
const toggleStreaming = (status, cur_conv_id, examNo) => {
  if(!cur_conv_id && examNo) {
    cur_conv_id = allConvs.value.find(conv => conv.examNo === examNo)?.id;
  }
  if(!status) {
    isStreaming.value = isStreaming.value.filter(cId => cId !== cur_conv_id);
  } else {
    isStreaming.value.push(cur_conv_id);
  }
}
// 切换暂停的状态
const togglePauseStreaming = (status, cur_conv_id) => {
  if(!status) {
    pauseStreaming.value = pauseStreaming.value.filter(cId => cId !== cur_conv_id);
  } else {
    pauseStreaming.value.push(cur_conv_id);
  }
}

// 设置外部系统传回来的检查信息等
const setInfoFromOuterSysValue = (examNo, key, value) => {
  if(!examNo) {
    return;
  }
  if(key === null) {
    delete infoFromOuterSys.value[examNo];
  }
  infoFromOuterSys.value = {
    ...infoFromOuterSys.value,
    [examNo]: {
      ...infoFromOuterSys.value[examNo],
      [key]: value,
    }
  }
}

// 获取外部系统传回的检查信息等
const getInfoFromOuterSysValue = (examNo, key) => {
  if(!examNo) {
    return;
  }
  if(!key) {
    return infoFromOuterSys.value[examNo] || null;
  }
  return infoFromOuterSys.value[examNo]?.[key] || null;
}

// 更新后的 sendMessage 函数, noFetch不发送消息请求
const sendMessage = (examInfo, noFetch) => {
  if(currentAiTool.value === '5' && !aiCaseInfo.value?.checkedCaseIds?.length) {
    message.error('请先选择知识库');
    return;
  }
  const sendExamNo = examInfo?.examNo || conv?.value?.examNo || '';
  if(sendExamNo) {
    setInfoFromOuterSysValue(sendExamNo, 'examInfoFromOuter', examInfo || null);
  }
  let currentConv = sendExamNo ? allConvs.value.find(conv => conv.examNo === sendExamNo) : conv.value;
  const user_input = currentConv.inputText.trim();
  const dbName = opts?.databases?.length > 0 ? opts.databases[meta.selectedKB]?.metaname : null;
  if (user_input) {
    toggleStreaming(true, currentConv.id);
    appendUserMessage(user_input, sendExamNo);
    appendAiMessage("", null, '', sendExamNo);
    const cur_res_id = currentConv.messages[currentConv.messages.length - 1].id;
    currentConv.inputText = '';
    meta.db_name = dbName;
    if(!noFetch) {
      fetchChatResponse(user_input, cur_res_id, currentConv.id);
    }
  } else {
    console.log('请输入消息');
  }
}

const retryMessage = (id) => {
  // 找到 id 对应的 message，然后删除包含 message 在内以及后面所有的 message
  const index = conv.value.messages.findIndex(msg => msg.id === id);
  const pastMessage = conv.value.messages[index-1]
  if(!pastMessage) {
    return;
  }
  conv.value.inputText = pastMessage.text
  // if (index !== -1) {
  //   conv.value.messages = conv.value.messages.slice(0, index-1);
  // }
  sendMessage();
}

// 清空等待pacs返回的定时
const clearTimeFromPacs = (examNo) => {
  let timer = getInfoFromOuterSysValue(examNo, 'resetFlagTimer');
  if(timer) {
    clearTimeout(timer);
    setInfoFromOuterSysValue(examNo, null);
  }
}

const autoSend = (msg, params) => {
  // 报告纠错且未返回检查信息时，先调pacs获取报告信息
  let sendExamNo = params ? params.examNo : (conv?.value?.examNo || '');
  clearTimeFromPacs(sendExamNo);
  if(!params && msg === constant.aiTypeAndName['7'] && sendExamNo) {
    setInfoFromOuterSysValue(sendExamNo, 'getExamInfoFlag', true);
    toggleStreaming(true, '', sendExamNo);
    window.aiSendToPacs({
      reqType: "rptData",  //报告数据
      param:{
        examNo: sendExamNo
      }
    }, () => {
      console.log(`向pacs请求检查信息：${Date.now()}-->${sendExamNo}`);
      let timer = setTimeout(() => {
        setInfoFromOuterSysValue(sendExamNo, 'getExamInfoFlag', false);
        appendUserMessage(msg, sendExamNo);
        appendAiMessage('抱歉，小睿暂时未获取到相关报告，无法为您进行报告纠错，请稍后重试！', null, 'customError', sendExamNo);
        toggleStreaming(false, '', sendExamNo);
      }, 3000)
      setInfoFromOuterSysValue(sendExamNo, 'resetFlagTimer', timer);
    }, () => {
      clearTimeFromPacs(sendExamNo);
    })
    return;
  }
  let currentConv = sendExamNo ? allConvs.value.find(conv => conv.examNo === sendExamNo) : conv.value;
  currentConv.inputText = msg;
  sendMessage(params);
}

// 删除会话
const deleteConvHandler = (id) => {
  conv.value.messages = conv.value.messages.filter(msg => msg.id !== id);
}

// 滚动分页加载
const handleScroll = () => {
  emit('chat-scroll', chatContainer.value);
}

// 切换患者
const changePatientHandler = (data) => {
  if(!data.examNo) {
    return;
  }
  emit('newconv', data.examNo, data)
}

const finishReasponHandler = (displayMessage) => {
  if(displayMessage.status === 'finished' && !displayMessage.isBubbleFlag) {
    setTimeout(() => {
      displayMessage.isBubbleFlag = true;
      displayMessage.splitByLetter = false;
    }, 2000)
  }
}

// 定位纠错的内容
const returnMarkHandler = (markList) => {
  window.aiSendToPacs({
    reqType: "rptErr",  //定位报告纠错
    param:{
      examNo: conv.value.examNo,
      reportNo: conv.value?.reportNo || '',
      errorWord: markList
    }
  }, () => {
    console.log(`向pacs发送报告纠错的词：${Date.now()}-->${conv.value.examNo}`);
  }, () => {
  })
}

// 切换AI工具助手
const toggleAiTool = (status) => {
  showMoreAiTool.value = status;
}

// 选择工具助手,type=''为默认睿图AI助手
const handleClickTool = (type) => {
  pauseFetchHandler();
  currentAiTool.value = type;
  showMoreAiTool.value = false;
  // 按助手类型重新打开
  emit('reopen-ai', type);
}

// 切换知识库
const changeCaseHandler = (data) => {
  let {checkedCaseIds, checkedCaseNames} = data;
  aiCaseInfo.value = {
    checkedCaseIds: checkedCaseIds,
    checkedCaseNames: checkedCaseNames,
  }
}

// 点击对话内容的标签note
const clickNoteHandler = async (e, msg) => {
  if(!e.target.classList?.contains('note')) {
    return;
  }
  let index = Number(e.target.innerText);
  if(isNaN(index)) {
    message.error('引用出错');
    return;
  }
  let contentInfo = await getNoteContent({
    chatId: conv.value.chatId || conv.value.id,
    contentId: msg.contentId,
    refNo: index,
  });
  if(contentInfo) {
    currentNote.value = contentInfo;
    showNoteDialog.value = true;
  }
}

// 引用常用词
const quoteKeyWordHandler = (word) => {
  if(isStreaming.value.includes(conv.value.id)) {
    message.error('对话正在进行中，请稍后再引用常用词');
    return;
  }
  conv.value.inputText = word;
}

// 从本地存储加载数据
onMounted(() => {
  scrollToBottom(chatContainer)
  // loadDatabases()
  const storedMeta = localStorage.getItem('meta');
  if (storedMeta) {
    const parsedMeta = JSON.parse(storedMeta);
    Object.assign(meta, parsedMeta);
  }
});

onBeforeUnmount(() => {
  if(resizeObserver) {
    resizeObserver.disconnect();
  }
});

// 监听 meta 对象的变化，并保存到本地存储
watch(
  () => meta,
  (newMeta) => {
    localStorage.setItem('meta', JSON.stringify(newMeta));
  },
  { deep: true }
);

watch(
  () => conv.value?.id,
  (newId) => {
    scrollToBottom(chatContainer);
  },
  { deep: true }
);
watch(
  () => scrollToBottomFlag.value,
  (newValue) => {
    if(newValue) {
      scrollToBottom(chatContainer);
    }
  },
  { deep: true }
);
// 暂无用到
// watch(
//   () => pauseFlag.value,
//   (newValue) => {
//     if(newValue) {
//       pauseFetchHandler();
//     }
//   },
//   { immediate: true, deep: true }
// );

// 暴露给父组件的内容
defineExpose({
  isStreaming,
  startObserveHeightToScroll,
  autoSend,
  showMoreAiTool,
})
</script>

<style lang="less" scoped>
.chat {
  position: relative;
  width: 100%;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  position: relative;
  box-sizing: border-box;
  flex: 5 5 200px;
  overflow-y: auto;
  background: url('@/assets/images/chat-bg.png') no-repeat top left;
  background-size: 100%;
  background-color: #F0F2F5;
  padding-top: var(--header-height);
  &.no-head {
    padding-top: 0;
  }
  .header {
    user-select: none;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 10;
    background-color: var(--header-bgcolor-4);
    height: var(--header-height);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;

    .header__left, .header__right {
      display: flex;
      align-items: center;
    }
    .header__center {
      font-weight: 600;
      font-size: 1rem;
      color: #000000;
      overflow: hidden;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .nav-btn {
    height: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    color: var(--gray-900);
    cursor: pointer;
    font-size: 1.5rem;
    width: auto;
    padding: 0.5rem 1rem;
    &.his {
      display: none;
    }

    .text {
      margin-left: 10px;
    }

    &:hover {
      background-color: var(--main-light-3);
    }
  }

  .tools-wrap {
    padding: 1.5rem 0.75rem;
    .tools-item {
      background: #FFF;
      font-weight: bold;
      font-size: 1rem;
      color: #000;
      padding: 0.75rem;
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      img {
        margin-right: 0.75rem;
      }
    }
  }
}
.metas {
  display: flex;
  gap: 8px;
}

.my-panal {
  position: absolute;
  margin-top: 5px;
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  padding: 12px;
  z-index: 11;
  width: 280px;

  .flex-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: var(--main-light-3);
    }

    .anticon {
      margin-right: 8px;
      font-size: 16px;
    }
    .ant-switch {
      &.ant-switch-checked {
        background-color: var(--main-500);
      }
    }
  }
}

.my-panal.r0.top100 {
  top: 100%;
  right: 0;
}

.my-panal.l0.top100 {
  top: 100%;
  left: 0;
}

.chat-examples {
  padding: 0 50px;
  text-align: center;
  position: absolute;
  top: 20%;
  width: 100%;
  z-index: 9;
  animation: slideInUp 0.5s ease-out;
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: #333;
  }

  .opts {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;

    .opt__button {
      background-color: var(--gray-200);
      color: #333;
      padding: .5rem 1.5rem;
      border-radius: 2rem;
      cursor: pointer;
      // border: 2px solid var(--main-light-4);
      transition: background-color 0.3s;
      // box-shadow: 0px 0px 10px 2px var(--main-light-4);


      &:hover {
        background-color: #f0f1f1;
        // box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.1);
      }
    }
  }

}

.chat-box {
  width: 100%;
  margin: 0 auto;
  flex-grow: 1;
  padding: 1rem 1rem 1rem 4rem;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  .more-box {
    position: relative;
    height: 1.5rem;
  }
  .message-box {
    position: relative;
    display: inline-block;
    border-radius: 1.5rem;
    margin: 0.8rem 0;
    padding: 0.56rem 0.75rem;
    user-select: text;
    word-break: break-word;
    font-size: 16px;
    font-variation-settings: 'wght' 400, 'opsz' 10.5;
    font-weight: 400;
    box-sizing: border-box;
    color: black;
    /* box-shadow: 0px 0.3px 0.9px rgba(0, 0, 0, 0.12), 0px 1.6px 3.6px rgba(0, 0, 0, 0.16); */
    /* animation: slideInUp 0.1s ease-in; */

    .role-avatar {
      position: absolute;
      left: -3rem;
      width: 2.25rem;
      height: 2.25rem;
      img {
        width: 100%;
        object-fit: contain;
      }
    }
    .err-msg {
      color: #eb8080;
      border: 1px solid #eb8080;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      text-align: left;
      background: #FFF5F5;
      margin-bottom: 10px;
      cursor: pointer;
    }

    .searching-msg {
      color: var(--gray-500);
    }
  }

  .message-box.sent {
    line-height: 24px;
    max-width: 95%;
    background: var(--main-100);
    align-self: flex-end;
    border-radius: 0.75rem 0.25rem 0.75rem 0.75rem;
    // .refs-com {
    //   display: none;
    //   &:hover {
    //     display: block !important;
    //   }
    // }
    &:hover {
      .refs-com {
        display: block;
      }
    }
  }

  .message-box.received {
    color: initial;
    width: fit-content;
    text-align: left;
    word-wrap: break-word;
    margin: 0;
    padding-bottom: 0;
    padding-top: 16px;
    padding-left: 0;
    padding-right: 0;
    text-align: justify;
    max-width: 100%;
  }

  p.message-text {
    max-width: 100%;
    word-wrap: break-word;
    margin-bottom: 0;
  }

  p.message-md {
    word-wrap: break-word;
    margin-bottom: 0;
  }
}

.bottom {
  position: sticky;
  bottom: 0;
  width: 100%;
  margin: 0 auto;
  padding: 0.5rem 0.75rem 0.75rem 0.75rem;
  background: #F0F2F5;
  .example-bottom {
    display: flex;
    flex-wrap: wrap;
    .example-card {
      background: #FFFFFF;
      box-shadow: 0rem 0.13rem 0.5rem 0rem rgba(0,0,0,0.05);
      border-radius: 0.5rem;
      padding: 0 0.5rem;
      color: #303133;
      font-size: 0.75rem;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: 0.5rem;
      &.disabled {
        cursor: not-allowed;
        pointer-events: none;
      }
      .example-img {
        margin-right: 0.1rem;
      }
      &:hover {
        color: var(--main-500);
      }
      &:first-child:last-child {
        margin-right: 0rem;
      }
    }
  }
  .input-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: auto;
    margin: 0 auto;
    padding: 0.25rem;
    border: 1px solid #E5E5E5;
    border-radius: 0.5rem;
    background: #fcfdfd;
    transition: background 0.3s, box-shadow 0.3s;
    &:focus-within {
      border: 1px solid var(--main-500);
      background: white;
    }
    &.ai-box {
      .input-wrap {
        flex: 1;
      }
    }
    .input-wrap {
      display: flex;
      align-items: center;
      width: 100%;
      height: auto;
      // margin: 0 auto;
      // padding: 0.25rem;
      // border: 1px solid #E5E5E5;
      // border-radius: 0.5rem;
      // background: #fcfdfd;
      // transition: background 0.3s, box-shadow 0.3s;
      .input-area {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        flex: 1;
      }
  
      textarea.user-input {
        flex: 1;
        height: 40px;
        background-color: transparent;
        border: none;
        margin: 0 0;
        color: #000;
        font-size: 0.88rem;
        outline: none;
        resize: none;
        word-break: break-all;
        &:focus {
          outline: none;
          box-shadow: none;
        }
  
        &:active {
          outline: none;
        }
        &::-webkit-scrollbar {
          width: 4px;
        }
      }
    }
    .ai-tool-wrap {
      margin-top: 0.5rem;
    }
  }

  button.ant-btn-icon-only {
    height: 32px;
    width: 32px;
    cursor: pointer;
    background-color: var(--main-color);
    border-radius: 50%;
    border: none;
    transition: color 0.3s;
    box-shadow: none;
    color: white;
    padding: 0;

    &:hover {
      background-color: var(--main-800);
    }

    &:disabled {
      background-color: var(--gray-400);
      cursor: not-allowed;
    }
  }
  .note {
    width: 100%;
    font-size: small;
    text-align: center;
    padding: 0rem;
    color: #ccc;
    margin: 4px 0;
    user-select: none;
  }
}



.ant-dropdown-link {
  color: var(--gray-900);
  cursor: pointer;
}



.chat::-webkit-scrollbar,
.chat-box::-webkit-scrollbar {
  position: absolute;
  width: 4px;
}

.chat::-webkit-scrollbar-track,
.chat-box::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.chat::-webkit-scrollbar-thumb,
.chat-box::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 4px;
}

.chat::-webkit-scrollbar-thumb:hover,
.chat-box::-webkit-scrollbar-thumb:hover {
  background: rgb(100, 100, 100);
  border-radius: 4px;
}

.chat::-webkit-scrollbar-thumb:active,
.chat-box::-webkit-scrollbar-thumb:active {
  background: rgb(68, 68, 68);
  border-radius: 4px;
}

.loading-dots {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.loading-dots div {
  width: 8px;
  height: 8px;
  margin: 0 4px;
  background-color: #666;
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 1.4s infinite ease-in-out both;
}

.loading-dots div:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots div:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes loading {0%,80%,100%{transform:scale(0.5);}40%{transform:scale(1);}}

.slide-out-left{-webkit-animation:slide-out-left .2s cubic-bezier(.55,.085,.68,.53) both;animation:slide-out-left .5s cubic-bezier(.55,.085,.68,.53) both}
.swing-in-top-fwd{-webkit-animation:swing-in-top-fwd .2s ease-out both;animation:swing-in-top-fwd .2s ease-out both}
@-webkit-keyframes swing-in-top-fwd{0%{-webkit-transform:rotateX(-100deg);transform:rotateX(-100deg);-webkit-transform-origin:top;transform-origin:top;opacity:0}100%{-webkit-transform:rotateX(0deg);transform:rotateX(0deg);-webkit-transform-origin:top;transform-origin:top;opacity:1}}@keyframes swing-in-top-fwd{0%{-webkit-transform:rotateX(-100deg);transform:rotateX(-100deg);-webkit-transform-origin:top;transform-origin:top;opacity:0}100%{-webkit-transform:rotateX(0deg);transform:rotateX(0deg);-webkit-transform-origin:top;transform-origin:top;opacity:1}}
@-webkit-keyframes slide-out-left{0%{-webkit-transform:translateX(0);transform:translateX(0);opacity:1}100%{-webkit-transform:translateX(-1000px);transform:translateX(-1000px);opacity:0}}@keyframes slide-out-left{0%{-webkit-transform:translateX(0);transform:translateX(0);opacity:1}100%{-webkit-transform:translateX(-1000px);transform:translateX(-1000px);opacity:0}}

@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
@keyframes slideInUp { from { transform: translateY(20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }

@media (max-width: 720px) {
  .chat {
    height: 100vh;
  }
  .chat .nav-btn.close {
    display: none;
  }
  .chat .nav-btn.his {
    display: flex;
  }

  .chat-container .chat .header {
    background: var(--header-bgcolor-4);
    .header__left, .header__right {
      gap: 8px;
    }

    .nav-btn {
      font-size: 1.5rem;
      padding: 0;

      &:hover {
        background-color: transparent;
        color: black;
      }

      .text {
        display: none;
      }
    }
  }

  .bottom {
    padding: 0.5rem 0.5rem;

    .input-box {
      border-radius: 8px;
      padding: 0.5rem;

      textarea.user-input {
        padding: 0;
      }
    }
    .note {
      display: none;
    }
  }
}

.controls {
  display: flex;
  align-items: center;
  gap: 8px;
  .search-switch {
    margin-right: 8px;
  }
}
</style>

<style lang="less">
.message-box {
  .ant-bubble-content {
    background-color: unset;
    border-radius: 0;
    padding: 0;
    white-space: pre-line;
    word-break: break-word;
    min-height: 0 !important;
    * {
      line-height: 1.25;
    }
    // br {
    //   line-height: 0; /* 设置 br 标签的行高为 0 */
    //   display: block; /* 确保 br 标签仍然起作用 */
    //   content: ""; /* 确保 br 标签仍然起作用 */
    //   margin-top: -14px; /* 可选：添加一些顶部间距以保持可读性 */
    // }
  }
  pre {
    border-radius: 8px;
    font-size: 14px;
    border: 1px solid var(--main-light-3);
    padding: 1rem;

    &:has(code.hljs) {
      padding: 0;
    }
  }
  .received-box {
    background: #FFFFFF;
    box-shadow: 0rem 0rem 0.25rem 0rem rgba(0,0,0,0.05);
    border-radius: 0.25rem 0.75rem 0.75rem 0.75rem;
    border: 0.06rem solid #DCDFE6;
    padding: 0.5rem 0.75rem;
    .received-tip, .received-qus {
      font-size: 1rem;
      color: #000000;
      line-height: 1.75rem;
      text-align: left;
    }
    .received-qus, .received-example {
      margin-top: 0.5rem;
    }
    .example {
      cursor: pointer;
      color: #002BFF;
      font-size: 1rem;
      line-height: 1.75rem;
      text-decoration: underline;
      &.disabled {
        cursor: not-allowed;
        pointer-events: none;
      }
      &:hover {
        opacity: 0.8;
      }
      & + .example {
        margin-left: 0.5rem;
      }
    }
  }
}
.message-md {
  word-wrap: break-word;
  // white-space: pre-line;
  font-size: 16px;
  .ant-typography {
    font-size: 16px;
  }
  .note {
    display: inline-block;
    cursor: pointer;
    margin-right: 0.25rem;
    background: url('@/assets/images/icons/note-icon.png') no-repeat center;
    color: transparent;
    position: relative;
    width: 25px;
    &:hover {
      opacity: 0.8;
    }
    &:before {
      color: #002BFF;
      content: '[';
      position: absolute;
      left: 0;
    }
    &:after {
      color: #002BFF;
      content: ']';
      position: absolute;
      right: 0;
    }
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: 1rem;
    line-height: 1.5 !important;
  }
  p {
    margin: 0;
  }
  li, ol, ul {
    margin: 0;
    padding-top: 0;
    padding-bottom: 0;
    & > p {
      margin: 0.25rem 0;
    }
  }
  ol, ul {
    line-height: 0.3 !important
  }
  table {
    border-collapse: collapse;
    th, td {
      border: 1px solid #9a9a9a;
      padding: 4px;
    }
  }
  h1,h2,h3,h4,h5 {
    margin-top: 0!important;
    margin-bottom: 0!important;
  }
}
</style>


