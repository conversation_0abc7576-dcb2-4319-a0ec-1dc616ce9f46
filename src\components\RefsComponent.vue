<template>
  <div :class="['refs', msg.role === 'sent' ? 'sent-ref' : '']" v-if="showRefs">
    <div class="tags">
      <!-- <span class="item"><BulbOutlined /> {{ msg.model_name }}</span> -->
      <span class="item btn" @click="copyText(msg.text)" v-if="msg.text && msg.text !== '<think>'"><CopyOutlined />复制</span>
      <!-- <span class="item btn" @click="deleteText(msg)"><DeleteOutlined />删除</span> -->
      <span class="item btn" v-if="showPosition" @click="returnMarkContent(msg.text)">定位</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import {copyToClipboard} from '@/hooks/utils'
import {
  CopyOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue';

const emit = defineEmits(['delete-conv', 'return-mark']);

const props = defineProps({
  message: Object,
  showPosition: Boolean,
})

const msg = ref(props.message)


// 定义 copy 方法
const copyText = async (text) => {
  copyToClipboard(text);
  // pacs不支持
  // if (isSupported) {
  //   try {
  //     await copy(text)
  //     message.success('文本已复制到剪贴板')
  //   } catch (error) {
  //     console.error('复制失败:', error)
  //     message.error('复制失败，请手动复制')
  //   }
  // } else {
  //   console.warn('浏览器不支持自动复制')
  //   message.warning('浏览器不支持自动复制，请手动复制')
  // }
}

// 定义 删除 方法
const deleteText = async (msg) => {
  emit('delete-conv', msg.id)
}

// 使用 reactive 创建一个响应式对象来存储每个文件的抽屉状态
const openDetail = reactive({})

// 初始化 openDetail 对象
for (const filename in msg.value.groupedResults) {
  openDetail[filename] = false
}

const showRefs = computed(() => msg.value.role=='sent' || (msg.value.role=='received' && msg.value.status=='finished'))


// 定位，将纠错的高两次返回pacs
const returnMarkContent = (text) => {
  if(!text) {
    message.error('没有内容');
    return;
  }
  const regex = /<mark\b[^>]*>(.*?)<\/mark>/g;;
  let match;
  const matches = [];

  while ((match = regex.exec(text)) !== null) {
    matches.push(match[1]);
  }
  if(!matches.length) {
    message.warning('未找到错误的内容进行定位');
    return;
  }
  emit('return-mark', matches);
}
</script>

<style lang="less" scoped>
.refs {
  display: flex;
  margin-bottom: 20px;
  color: var(--gray-500);
  font-size: 13px;
  gap: 10px;
  &.sent-ref {
    position: absolute;
    left: 0px;
    top: 100%;
    width: max-content;
  }

  .item {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: 0 5px;
    border-radius: 6px;
    font-size: 13px;
    user-select: none;

    &.btn {
      cursor: pointer;
      &:hover {
        background: var(--gray-200);
      }
      &:active {
        background: var(--gray-300);
      }
    }
  }

  .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    .filetag {
      display: flex;
      align-items: center;
      gap: 5px;
    }
  }
}
</style>