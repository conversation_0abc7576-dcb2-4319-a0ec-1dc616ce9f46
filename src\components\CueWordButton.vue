<template>
  <div :class="{'key-word-btn': true}" @click="openWordDialog">
    <img src="@/assets/images/icons/tip-icon.png" alt="" class="tip-img">常用提示词
  </div>
  <a-modal 
    v-model:open="showDialog" 
    title="常用提示词"
    cancelText="关闭"
    okText="提示词管理"
    style="width: fit-content;max-width: 100%;"
    @ok="goToSetting"
  >
    <Spin class="full-spin full-spin-layer" v-if="loading"></Spin>
    <a-radio-group v-model:value="privateFlag" button-style="solid" @change="getCueWordListHandler">
      <a-radio-button value="0">公有</a-radio-button>
      <a-radio-button value="1">私有</a-radio-button>
    </a-radio-group>
    <div class="key-word-list" v-if="cueWordList.length">
      <div class="key-word-item" v-for="(item, index) in cueWordList" :key="index">
        <div class="cueword">{{item.paramName}}</div>
        <div class="quote-btn" @click="quoteWord(item.paramValue)">
          <img src="@/assets/images/icons/quote-icon.png" alt="">
          引用
        </div>
      </div>
    </div>
    <div class="nodata-wrap" v-else>暂无数据</div>
  </a-modal>
  <CueWordSettingDialog
    :showDialog.sync="showSettingDialog"
    :privateFlag="privateFlag"
    :state="props.state"
    @update:showDialog="(newVal) => showSettingDialog = newVal"
    @close="closeSettingDialog"
  ></CueWordSettingDialog>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message, Spin } from 'ant-design-vue'
import CueWordSettingDialog from '@/components/CueWordSettingDialog.vue'
import { getCueWordList } from '@/hooks/useChat';
const emit = defineEmits(['quoteKeyWord']);
const props = defineProps({
  state: Object,
  disabled: {
    type: Boolean,
    default: false
  }
});

const showDialog = ref(false);   //是否显示常用词
const showSettingDialog = ref(false);   //是否显示常用词管理
const privateFlag = ref('1');  // 1：私有 0：公有
let cueWordList = ref([]);
const loading = ref(false);

// 打开提示词
const openWordDialog = async () => {
  await getCueWordListHandler();
  showDialog.value = true;
}

// 获取提示词列表
const getCueWordListHandler = async () => {
  cueWordList.value = [];
  loading.value = true;
  let params = {
    userId: props.state?.userInfo?.userId,
    privateFlag: privateFlag.value,   // 1：私有 0：公有
  }
  let res = await getCueWordList(params);
  cueWordList.value = res || [];
  loading.value = false;
}

// 引用提示词
const quoteWord = (word) => {
  if(props.disabled) {
    message.error('对话正在进行中，请稍后再引用常用词');
    return;
  }
  if(!word) {
    message.error('常用词不能为空');
    return;
  }
  emit('quoteKeyWord', word);
  showDialog.value = false;
}

// 前往提示词管理
const goToSetting = () => {
  showSettingDialog.value = true;
}

// 关闭提示词管理
const closeSettingDialog = (flag) => {
  privateFlag.value = flag;
  getCueWordListHandler();
}

</script>

<style scoped lang="less">
.key-word-btn {
  background: #FFFFFF;
  box-shadow: 0rem 0.13rem 0.5rem 0rem rgba(0,0,0,0.05);
  border-radius: 0.5rem;
  padding: 0 0.5rem;
  color: #303133;
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
    pointer-events: none;
  }
  .tip-img {
    margin-right: 0.1rem;
  }
  &:hover {
    color: var(--main-500);
  }
}
.key-word-list {
  min-height: 20rem;
  max-height: 50vh;
  overflow: auto;
  .key-word-item {
    margin-top: 0.75rem;
    background: linear-gradient( 315deg, #E8F5FF 0%, #FDF3FF 100%);
    border-radius: 0.5rem;
    border: 0.06rem solid rgba(0,0,0,0.05);
    padding: 0.63rem 0.75rem;
    display: flex;
    justify-content: space-between;
    font-size: 0.88rem;
    min-width: 25rem;
    .cueword {
      flex: 1;
      margin-right: 0.5rem;
      color: #303133;
      word-break: break-all;
      white-space: pre-line;
    }
    .quote-btn {
      display: flex;
      align-items: center;
      color: #175CE6;
      cursor: pointer;
      &:hover {
        opacity: 0.7;
      }
      img {
        margin-right: 0.2rem;
      }
    }
  }
}
.nodata-wrap {
  width: 25rem;
  margin-top: 0.5rem;
  color: #666;
}
@media (max-width: 520px) {
  .key-word-list {
    .key-word-item {
      min-width: 20rem;
    }
  }
}
</style>
