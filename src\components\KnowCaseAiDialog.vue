<template>
  <a-button class="knowcase-btn" type="primary" ghost @click="openCaseDialog">
    <img src="@/assets/images/icons/chat-know-icon.png" alt="">
    <span>{{useCaseData?.checkedCaseNames?.join('；')}}</span>
    <DownOutlined />
  </a-button>
  <a-modal 
    v-model:open="showKnowcaseDialog" 
    title="知识库选择"
    cancelText="取消"
    okText="确认"
    @ok="confirmSelectCase"
  >
    <div class="case-list" v-if="caseData?.length">
      <div class="case-item" v-for="(parent, index) in caseData" :key="parent.id">
        <div class="case-parent">
          <a-checkbox 
            v-model:checked="parent.parentChecked"
            @change="toggleCaseParentChecked(parent)"
          >{{parent.name}}</a-checkbox>
        </div>
        <div class="case-child" v-if="parent.children?.length">
          <a-checkbox-group v-model:value="parent.childChecked" @change="toggleCaseChildChecked(parent, child)">
            <a-checkbox 
              v-for="(child, cI) in parent.children" 
              :key="child.dataSetId"
              :value="child.dataSetId"
            >{{child.dataSetName}}</a-checkbox>
          </a-checkbox-group>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, toRefs, reactive, onMounted } from 'vue';
import { DownOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue'
import {request} from '@/hooks/utils.js';
import api from '@/config/api.js';
const emit = defineEmits(['changeCase']);
const props = defineProps({
  state: Object,
  aiCaseInfo: Object
});

const showKnowcaseDialog = ref(false);  //选择知识库
let caseData = reactive([]);  //知识库数据
let useCaseData = ref({});  //使用的知识库数据

// 获取知识库分组
const getCaseGroup = async () => {
  let params = {
    userId: props.state?.userInfo?.userId
  };
  caseData = [];
  let res = await request(api.getDataSets, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '获取知识库分组失败');
    return;
  }
  let data = res.result || [];
  let {checkedCaseIds = []} = useCaseData.value;
  const handler = (id, name) => {
    let childData = [];
    // 私有
    if(id === 'private') {
      childData = data.filter(item => item.privateFlag === '1') || [];
    } else {
      childData = data.filter(item => item.privateFlag !== '1') || [];
    }
    // 初始化父级和子级的 checked 状态
    if (childData.length) {
      let curChildIds = childData.filter(child => checkedCaseIds.includes(child.dataSetId)) || [];
      let obj = ref({
        id,
        name,
        parentChecked: curChildIds.length === childData.length, // 初始值为 false
        childChecked: curChildIds.map(child => child.dataSetId) || [],     // 子级选中状态
        children: childData
      });
      caseData.push(obj.value);
    }
  }
  handler('public', '公有知识库');
  handler('private', '私有知识库');
};

// 勾选全部父级
const toggleCaseParentChecked = (parent) => {
  let childIds = parent.children.map(child => child.dataSetId);

  if (parent.parentChecked) {
    // 如果父级被选中，添加子级到 caseChildChecked 并去重
    parent.childChecked = [...new Set([...parent.childChecked, ...childIds])];
  } else {
    // 如果父级被取消选中，移除对应的子级
    parent.childChecked = parent.childChecked.filter(id => !childIds.includes(id));
  }
};

// 勾选全部子级
const toggleCaseChildChecked = (parent, child) => {
  let childCheckedLen = parent.childChecked?.length || 0;
  parent.parentChecked = childCheckedLen === parent.children.length
}

// 确定选中
const confirmSelectCase = () => {
  let checkedCaseIds = [];
  let checkedCaseNames = [];
  caseData.forEach(item => {
    if(item.childChecked?.length) {
      checkedCaseIds = [...checkedCaseIds, ...item.childChecked];
      let name = item.children.filter(child => item.childChecked.includes(child.dataSetId))
        .map(child => child.dataSetName);
      checkedCaseNames = [...checkedCaseNames, ...name];
    }
  });
  if(!checkedCaseIds.length) {
    message.error('请选择知识库');
    return;
  }
  showKnowcaseDialog.value = false;
  useCaseData.value = {checkedCaseIds, checkedCaseNames};
  emit('changeCase', useCaseData.value);
}

// 打开知识库弹框
const openCaseDialog = async () => {
  await getCaseGroup();
  showKnowcaseDialog.value = true;
}
onMounted(() => {
  if(props.aiCaseInfo) {
    useCaseData.value = props.aiCaseInfo;
  }
})
</script>

<style lang="less">
.knowcase-btn {
  display: flex;
  align-items: center;
  background: #ECF5FF;
  img {
    margin-right: 0.25rem;
  }
}
.case-list {
  .case-item {
    margin-bottom: 1.25rem;
    .case-parent {
      background: #F5F7FA;
      font-weight: bold;
      font-size: 1rem;
      color: #303133;
      padding: 0.5rem 1rem;
      border-radius: 0.5rem;
    }
    .case-child {
      font-size: 0.88rem;
      color: #303133;
      margin-top: 0.75rem;
      padding: 0 1rem;
      .ant-checkbox-wrapper {
        width: 32%;
        align-items: flex-start;
        align-self: center;
        .ant-checkbox + span {
          word-break: break-all;
          white-space: pre-line;
        }
      }
    }
  }
}
@media (max-width: 520px) {
  .case-list {
    .case-item {
      .case-child {
        .ant-checkbox-wrapper {
          width: 48%;
        }
      }
    }
  }
}
</style>
