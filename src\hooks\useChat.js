import {h, ref} from 'vue';
import { Typography } from 'ant-design-vue'
import markdownit from 'markdown-it';
import {Marked} from 'marked';
import {request} from '@/hooks/utils.js';
import api from '@/config/api.js';
import { message } from 'ant-design-vue'
// const md = markdownit({ html: true, breaks: true });
const md = new Marked({ html: true, breaks: true });
// 绘制聊天内容
export const useMarkChatContent = () =>{
  // const marked = new Marked(
  //   {
  //     gfm: true,
  //     breaks: true,
  //     tables: true,
  //   },
  //   markedHighlight({
  //     langPrefix: 'hljs language-',
  //     highlight(code) {
  //       return hljs.highlightAuto(code).value;
  //     }
  //   })
  // );

  // const renderMarkdown = (msg) => {
  //   if(!msg) {
  //     return '';
  //   }
  //   if(msg?.text === undefined) {
  //     return '';
  //   }
  //   if (msg.status === 'loading') {
  //     return marked.parse(msg.text + '🟢')
  //   } else {
  //     return marked.parse(msg.text)
  //   }
  // }

  const bubbleRenderMessage = (content) => {
    return h(Typography, null, {
      // default: () => h('div', { innerHTML: md.render(content) }),
      default: () => h('div', { innerHTML: md.parse(content) }),
    });
  };

  const aiBubbleType = ref({ step: 2, interval: 50 });  //AI回答输出样式

  let resizeObserver = null;
  const heightObserver = (element, oldHeight, scrollFun) => {
    resizeObserver = new ResizeObserver(() => {
      let height = element?.clientHeight || 0;
      if(height !== oldHeight) {
        oldHeight = height;
        scrollFun();
      }
    });
  
    if (element) {
      resizeObserver.observe(element);
    }
  }

  const toggleCollapse = () => {
    if(resizeObserver) {
      resizeObserver.disconnect();
    }
  }

  return {
    resizeObserver,
    // renderMarkdown,
    bubbleRenderMessage,
    aiBubbleType,
    heightObserver,
    toggleCollapse
  }
}

// 消息相关
export const useMessage = () => {
  // 找到当前消息
  const findCurrentMessage = (id, allConvList) => {
    if(!allConvList?.length) {
      return {};
    }
    for(let oneConv of allConvList) {
      if(!oneConv.messages?.length) {
        continue;
      }
      let messages = oneConv.messages;
      for(let message of messages) {
        if(message.id === id) {
          return {msg: message, convId: oneConv.id};
        }
      }
    }
    return {};
  };

  const scrollToBottom = (container) => {
    setTimeout(() => {
      if(container.value) {
        container.value.scrollTop = container.value.scrollHeight - container.value.clientHeight
      }
    }, 10)
  }

  const groupRefs = (id, allConvs, chatContainer) => {
    // const msg = conv.value.messages.find((msg) => msg.id === id)
    const {msg, convId} = findCurrentMessage(id, allConvs);
    if (msg.refs && msg.refs.knowledge_base?.results?.length > 0) {
      msg.groupedResults = msg.refs.knowledge_base.results
      .filter(result => result.file && result.file.filename)
      .reduce((acc, result) => {
        const { filename } = result.file;
        if (!acc[filename]) {
          acc[filename] = []
        }
        acc[filename].push(result)
        return acc;
      }, {})
    }
    scrollToBottom(chatContainer)
  }

  const updateMessage = (info, conv, allConvs, chatContainer) => {
    const {msg, convId} = findCurrentMessage(info.id, allConvs);
    if (msg) {
      try {
        if(info.splitByLetter) {
          msg.splitByLetter = info.splitByLetter;
        }
        // 只有在 text 不为空时更新
        if (info.text !== null && info.text !== undefined && info.text !== '') {
          if(info.splitByLetter) {
            msg.text += info.text;
          } else {
            msg.text = info.text;
          }
          msg.showThinking = 'no';
          msg.noShowThinkBubble = true;
        }
  
        if (info.reasoning_content !== null && info.reasoning_content !== undefined && info.reasoning_content !== '') {
          if(info.splitByLetter) {
            msg.reasoning_content += info.reasoning_content;
          } else {
            msg.reasoning_content = info.reasoning_content;
          }
        }
  
        // 只有在 refs 不为空时更新
        if (info.refs !== null && info.refs !== undefined) {
          msg.refs = info.refs;
        }
  
        if (info.model_name !== null && info.model_name !== undefined && info.model_name !== '') {
          msg.model_name = info.model_name;
        }
  
        // 只有在 status 不为空时更新
        if (info.status !== null && info.status !== undefined && info.status !== '') {
          msg.status = info.status;
        }
  
        if (info.meta !== null && info.meta !== undefined) {
          msg.meta = info.meta;
        }
  
        if (info.message !== null && info.message !== undefined) {
          msg.message = info.message;
        }
  
        if (info.showThinking !== null && info.showThinking !== undefined) {
          msg.showThinking = info.showThinking;
        }
        if (info.question !== null && info.question !== undefined) {
          msg.question = info.question;
        }

        // 更新知识库文档内容
        if (info.status === 'finished' && info.chatRefList) {
          msg.chatRefList = info.chatRefList;
        }
        if (info.status === 'finished' && info.chunks) {
          msg.chunks = info.chunks;
        }
        if (info.status === 'finished' && info.contentId) {
          msg.contentId = info.contentId;
        }
        // 是当前查看的对话时，滚动到底部
        if(convId === conv?.value?.id) {
          setTimeout(() => {
            scrollToBottom(chatContainer);
          },100)
        }
      } catch (error) {
        console.error('Error updating message:', error);
        msg.status = 'error';
        msg.text = '消息更新失败';
      }
    } else {
      console.error('Message not found:', info.id);
    }
  };

  return {
    findCurrentMessage,
    scrollToBottom,
    groupRefs,
    updateMessage
  }
}

// 获取文章地址
export const getDocUrl = async (params) => {
  let res = await request(api.getDocUrl, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '操作失败');
    return;
  }
  if(!res.result) {
    message.error('找不到引用地址');
    return;
  }
  return res.result;
}

// 获取引用的块内容
export const getNoteContent = async (params) => {
  let res = await request(api.getNoteContent, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '操作失败');
    return;
  }
  if(!res.result) {
    message.error('找不到引用内容');
    return;
  }
  return res.result;
}

// 获取常用词列表
export const getCueWordList = async (params) => {
  let res = await request(api.getSaveQuestion, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '操作失败');
    return;
  }
  return res.result || [];
}

// 保存常用词
export const saveCueWord = async (params) => {
  let res = await request(api.saveQuestion, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '操作失败');
    return false;
  }
  message.success('操作成功');
  return true;
}

// 删除常用词
export const deleteCueWord = async (params) => {
  let res = await request(api.delSaveQuestion, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '操作失败');
    return false;
  }
  message.success('操作成功');
  return true;
}