{"name": "webpacs-ai-assistant", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "serve": "vite serve --host", "build": "node build_script vite build --mode production", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@antv/g6": "^5.0.17", "@vueuse/core": "^10.11.0", "ant-design-vue": "^4.2.3", "ant-design-x-vue": "^1.0.5", "axios": "^1.3.4", "d3": "^7.8.3", "echarts": "^5.4.2", "echarts-gl": "^2.0.9", "highlight.js": "^11.10.0", "less": "^4.1.3", "markdown-it": "^14.1.0", "marked": "^13.0.2", "marked-highlight": "^2.1.4", "pinia": "^2.0.32", "rt-common-functions": "^1.3.6", "rt-request": "^1.0.9", "vue": "^3.5.13", "vue-router": "^4.1.6"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-prettier": "^7.1.0", "eslint": "^9.15.0", "eslint-plugin-vue": "^9.9.0", "prettier": "^2.8.4", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^4.1.4"}}