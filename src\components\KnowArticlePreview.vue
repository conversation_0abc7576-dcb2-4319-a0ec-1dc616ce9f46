<template>
  <a-modal 
    v-model:open="open" 
    :footer="null"
    class="know-article-preview"
    style="width: 100%;top:50px;"
  >
    <div class="content">
      <iframe :src="docUrl" frameborder="0"></iframe>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, toRefs, watch } from 'vue';
const emit = defineEmits(['update:showDialog']);
const props = defineProps({
  showDialog: {
    type: Boolean,
    default: false
  },
  docUrl: {
    type: String,
    default: ''
  },
});

const open = ref(false);

watch(() => props.showDialog, (newVal) => {
  open.value = newVal;
})
watch(() => open.value, (newVal) => {
  emit('update:showDialog', newVal);
})
</script>

<style lang="less">
.know-article-preview {
  .ant-modal-close{
    right: 5px;
  }
  .content {
    width: 100%;
    height: 50rem; 
    // overflow: auto;
    border-radius: 0.44rem;
    border: 1px solid #DCDFE6;
    iframe {
      width: 100%;
      height: 100%;
    }
  }
}
// @media (max-width: 520px) {
//   .know-article-preview {
//     max-width: 100% !important;
//   }
// }
@media (min-width: 980px) {
  .know-article-preview {
    width: 70% !important;
  }
}
</style>
