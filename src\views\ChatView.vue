<template>
  <div class="chat-container">
    <Spin class="full-spin full-spin-layer" v-if="isLoadListing"></Spin>
    <div class="conver-layer" 
      v-if="state.isSidebarOpen && state.miniSideBarOpen"
      @click="state.isSidebarOpen = false"
    ></div>
    <div 
      :class="['conversations', { 'is-open': state.isSidebarOpen && state.miniSideBarOpen }]"
      v-if="!childRef?.showMoreAiTool && !isThird"
    >
      <div class="actions">
        <!-- <span class="his-title">历史对话仅展示最近3个月的对话</span> -->
        <span class="his-title">历史对话</span>
        <div class="action close" @click="state.isSidebarOpen = false">
          <MenuOutlined class="menu-icon"/>
          <CloseOutlined class="close-icon" />
        </div>
      </div>
      <div class="conversation-list" ref="conversation" v-if="!isThird">
        <div class="conversation"
          v-for="(state, index) in convs"
          :key="index"
          :class="{ active: curConvId === index }"
          @click="goToConversation(index)">
          <div class="conversation__title"><CommentOutlined /> &nbsp;{{ state.title }}</div>
          <Popconfirm placement="top" ok-text="确定" cancel-text="取消" @confirm.stop="delConv(index)">
            <template #title>
              <p>确定要删除该对话？</p>
            </template>
            <div class="conversation__delete" @click.stop="()=>{return false;}"><DeleteOutlined /></div>
          </Popconfirm>
        </div>
      </div>
    </div>
    <ChatComponent
      :conv="convs[curConvId]"
      :allConvs="convs"
      :state="state"
      :isLoadMsg="isLoadMsg"
      :isLoadMoreMsg="isLoadMoreMsg"
      :scrollToBottomFlag="scrollToBottomFlag"
      :pauseFlag="pauseFlag"
      :hasMoreFlag="hasMoreFlag"
      ref="childRef"
      @rename-title="renameTitle"
      @newconv="addNewConv"
      @chat-scroll="onChatScroll"
      @reopen-ai="reopenAiByType"
    />
  </div>
</template>

<script setup>
import { reactive, ref, watch, onMounted, nextTick } from 'vue'
import { MenuOutlined, CloseOutlined, DeleteOutlined, CommentOutlined } from '@ant-design/icons-vue'
import { Spin, message, Popconfirm } from 'ant-design-vue'
import ChatComponent from '@/components/ChatComponent.vue'
import api from '@/config/api.js'
import {request, generateRandomHash} from '@/hooks/utils.js'
import { aiToolList } from '@/config/constant.js'
const childRef = ref(null)
const conversation = ref(null)

const pauseFlag = ref(false);  //是否暂停
const scrollToBottomFlag = ref(false);  //是否滚至底部
const isLoadListing = ref(true);  //是否正在加载列表
const isLoadMsg = ref(false);   //是否正在加载对话
const isLoadMoreMsg = ref(false);  //是否加载更多对话
const hasMoreFlag = ref(true);  //是否还有更多对话
const pageNo = ref(0);  //对话详情分页
const pageSize = ref(15);  //对话详情分页
const totalPage = ref(0);  //总页数
const currentAiTool = ref('');

const state = reactive({
  isSidebarOpen: JSON.parse(localStorage.getItem('chat-sidebar-open') || 'true'),
  miniSideBarOpen: JSON.parse(localStorage.getItem('mini-chat-sidebar-open') || 'true'),
  userInfo: JSON.parse(sessionStorage.getItem('ai_general_info') || '{}'),
})

let curExamNo = state.userInfo?.busId;
let curNewConvName = curExamNo && (state.userInfo?.name || state.userInfo?.patLocalId) ? `${state.userInfo?.name || ''}(${state.userInfo?.patLocalId || ''})` : '';
let convs = reactive([
  {
    id: generateRandomHash(8),
    title: curNewConvName || '新对话',
    history: [],
    messages: [],
    inputText: '',
    examNo: curExamNo || '',
    reportNo: state.userInfo?.reportNo || ''
  },
])

const isThird = ref(state.userInfo?.third === '1');

// Watch isSidebarOpen and save to localStorage
watch(
  () => state.isSidebarOpen,
  (newValue) => {
    state.miniSideBarOpen = newValue;
    localStorage.setItem('chat-sidebar-open', JSON.stringify(newValue))
  }
)
watch(
  () => state.miniSideBarOpen,
  (newValue) => {
    state.isSidebarOpen = newValue;
    localStorage.setItem('mini-chat-sidebar-open', JSON.stringify(newValue))
  }
)

// Watch convs and save to localStorage
watch(
  () => convs,
  (newStates) => {
    localStorage.setItem('chat-convs', JSON.stringify(newStates))
  },
  { deep: true }
)
const curConvId = ref(0);   //当前选择的对话

const renameTitle = (newTitle) => {
  convs[curConvId.value].title = newTitle
}

const goToConversation = (index) => {
  if(curConvId.value === index) {
    return;
  }
  if(childRef?.value?.startObserveHeightToScroll) {
    childRef?.value?.startObserveHeightToScroll();
  }
  
  pauseFlag.value = false;
  curConvId.value = index;
  if(document.documentElement.clientWidth <= 720) {
    state.miniSideBarOpen = false;
  }
  resetDefaultHandler();
  getChatDetail(convs[index].id, 0);
}

const addNewConv = async (examNo, examInfo) => {
  pauseFlag.value = false;
  resetDefaultHandler();
  let newconvIndex = convs?.findIndex(item => !item.examNo);
  if (!examNo && newconvIndex > -1 && convs[newconvIndex].messages?.length === 0) {
    curConvId.value = newconvIndex;
    return;
  }
  if(examNo) {
    curExamNo = examNo;
  }
  if(examInfo) {
    state.userInfo = {...state.userInfo, busId: curExamNo, ...examInfo};
    sessionStorage.setItem('ai_general_info', JSON.stringify(state.userInfo));
    curNewConvName =  curExamNo && (examInfo?.name || examInfo?.patLocalId) ? `${examInfo?.name || ''}(${examInfo?.patLocalId || ''})` : '';
  }
  let isExitIndex = convs.findIndex(item => examNo && item.examNo === examNo)
  let isExitCurrConv = isExitIndex > -1;
  if(!examNo || !isExitCurrConv) {
    curConvId.value = 0;
    convs.unshift({
      id: generateRandomHash(8),
      title: (examNo && curNewConvName) ? curNewConvName : `新对话`,
      history: [],
      messages: [],
      inputText: '',
      examNo: examNo || '',
      reportNo: examInfo?.reportNo || '',
    })
  } else {
    curConvId.value = isExitIndex;
    // 未获取过先获取历史对话详情
    if(!convs[curConvId.value]?.messages?.length) {
      await getChatDetail(convs[isExitIndex].id, 0);
    }
    if(examInfo.reportNo) {
      convs[isExitIndex].reportNo = examInfo.reportNo;
    }
  }
  nextTick(() => {
    // 非知识库的模式下,新建的对话检查顺便发一次‘病历总结’
    let aiTypeArr = aiToolList.map(item => item.type);
    if(!aiTypeArr.includes(currentAiTool.value) && examNo && !examInfo?.question && !convs[curConvId.value]?.messages?.length) {
      if(childRef?.value?.autoSend) {
        childRef?.value?.autoSend('病历总结', {...(examInfo || {}), examNo});
      }
    }
    // 将带question的question传给autoSend
    if(examNo && examInfo?.question) {
      if(childRef?.value?.autoSend) {
        childRef?.value?.autoSend(examInfo.question, examInfo);
      }
    }
    // 定位至列表处conversation-list
    scrollToConv();
  })
}

// 定位至列表处conversation-list
const scrollToConv = () => {
  nextTick(() => {
    if(conversation?.value) {
      conversation.value.scrollTop = document.querySelector('.conversation.active')?.offsetTop;
    }
  })
}

const delConv = async (index) => {
  let params = {
    id: convs[index].id,
    userId: state.userInfo?.userId,
    busType: currentAiTool.value || state.userInfo?.busType,
  }
  let res = await request(api.delHistroy, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '操作失败');
    return;
  }
  convs.splice(index, 1)
  if (index < curConvId.value) {
    curConvId.value -= 1
  } else if (index === curConvId.value) {
    curConvId.value = 0
  }
  if (convs.length === 0) {
    addNewConv()
  } else {
    getChatDetail(convs[curConvId.value].id, 0);
  }
}

// 切换详情，停止获取内容，恢复部分值
const resetDefaultHandler = () => {
  hasMoreFlag.value = false;
  // pauseFlag.value = true;
}

// 按类型重新打开AI
const reopenAiByType = (type) => {
  currentAiTool.value = type;
  getHistoryList(type ? '' : curExamNo, type);
}

// 获取历史列表
const getHistoryList = async (examNo, aiType) => {
  isLoadListing.value = true;
  let params = {
    userId: state.userInfo?.userId,
    busType: aiType || currentAiTool.value || state.userInfo?.busType,
  };
  convs = reactive([]);
  let res = await request(api.historyList, 'post', params);
  if (!res || res.status !== '0') {
    hasMoreFlag.value = false;
    message.error(res?.message || '操作失败');
    isLoadListing.value = false;
    return;
  }
  let data = res.result || [];
  if(data?.length) {
    resetConvList(data);
    // 如果存在检查自动定位至该检查，否则第一条
    let index = 0;
    if(examNo) {
      index = data.findIndex(item => item.examNo === examNo);
      if(index === -1) {
        hasMoreFlag.value = false;
        addNewConv(examNo);
        index = 0;
      } else {
        curConvId.value = index;
        getChatDetail(data[index].id, 0);
      }
      scrollToConv();
    } else {
      addNewConv();
      // curConvId.value = index;
      // getChatDetail(data[index].id, 0);
    }
  } else {
    // ai助手打开新建对话
    if(aiType) {
      addNewConv();
    } else {
      addNewConv(curExamNo, state.userInfo);
    }
    hasMoreFlag.value = false;
  }
  isLoadListing.value = false;
}

// 获取对话详情
const getChatDetail = async(chatId, currentPageNo) => {
  let isLoadStreaming = childRef?.value?.isStreaming?.includes(chatId);  //正在加载对话流中
  scrollToBottomFlag.value = false;
  if(currentPageNo === 0 && !isLoadStreaming) {
    convs[curConvId.value].messages = [];
    convs[curConvId.value].history = [];
  }
  if(isLoadMsg.value || isLoadMoreMsg.value) {
    // 正在加载中
    return;
  }
  if(!chatId) {
    message.error('获取对话失败，不存在id');
    return;
  }
  let curLoadStatusKey = currentPageNo === 0 ? isLoadMsg : isLoadMoreMsg;
  currentPageNo !== undefined && (pageNo.value = currentPageNo);
  curLoadStatusKey.value = true;
  let params = {
    id: chatId,
    userId: state.userInfo?.userId,
    busType: currentAiTool.value || state.userInfo?.busType,
    pageNo: currentPageNo !== undefined ? currentPageNo : pageNo.value,
    pageSize: pageSize.value,
  };
  let res = await request(api.historyInfo, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '操作失败');
    curLoadStatusKey.value = false;
    return;
  }
  let data = res.result || [];
  let page = res.page || {};
  totalPage.value = page.total || 0;
  hasMoreFlag.value = pageNo.value < Math.ceil(totalPage.value / pageSize.value) - 1;
  let reverseData = data.reverse(); //数组倒着显示
  reverseData.map(reItem => {
    if(reItem.messages?.length) {
      reItem.messages.map(msg => msg.isBubbleFlag = true);
    }
  })
  if(pageNo.value === 0) {
    if(!isLoadStreaming) {
      convs[curConvId.value].messages = reverseData;
      convs[curConvId.value].history = reverseData;
    }
    scrollToBottomFlag.value = true;
  } else {
    convs[curConvId.value].messages = [...reverseData, ...convs[curConvId.value].messages];
    convs[curConvId.value].history = [...reverseData, ...convs[curConvId.value].messages];
  }
  curLoadStatusKey.value = false;
}

// 滚动分页加载对话详情
const onChatScroll = (container) => {
  if(!hasMoreFlag.value) {
    return;
  }
  let currentPageNo = pageNo.value + 1;
  if(currentPageNo > Math.ceil(totalPage.value / pageSize.value) - 1) {
    return;
  }
  const { scrollTop, scrollHeight, clientHeight } = container;
  // if (scrollTop + clientHeight >= scrollHeight - 10) { // 当滚动到底部时加载更多数据
  // }
  if(scrollTop === 0) {
    pageNo.value = currentPageNo;
    // 拉至顶部
    getChatDetail(convs[curConvId.value].id, currentPageNo);
  }
}

// 赋值对话历史列表
const resetConvList = (data) => {
  if (data) {
    for (let i = 0; i < data.length; i++) {
      let item = data[i];
      item.title = item.title || item.chatTitle || `新对话`;
      item.id = item.id || item.chatId || '';
      convs[i] = item;
    }
  }
}
// Load convs from localStorage on mount
onMounted(() => {
  if(document.documentElement.clientWidth <= 720) {
    state.miniSideBarOpen = false;
  }
  if(isThird.value && state.userInfo?.type === 'detail') {
    isLoadListing.value = false;
    getChatDetail(state.userInfo?.chatId, 0);
  } else {
    getHistoryList(curExamNo);
  }
})
</script>

<style lang="less" scoped>
@import '@/assets/main.css';

.chat-container {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
}
.chat-container .conversations:not(.is-open) {
  width: 0;
  opacity: 0;
  flex: 0 0 0;
}

.chat-container .conversations.is-open {
  overflow: hidden; /* 确保内容不溢出 */
  white-space: nowrap; /* 防止文本换行 */
  flex: 1 1 auto; /* 当侧边栏打开时，占据可用空间 */
}
.conversations {
  display: block;
  width: 230px; /* 初始宽度 */
  max-width: 230px;
  border-right: 1px solid var(--main-light-3);
  overflow: hidden; /* 确保内容不溢出 */
  max-height: 100%;
  background-color: var(--bg-sider);

  .actions {
    height: var(--header-height);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 9;
    border-bottom: 1px solid var(--main-light-3);
    background: #F2F6FC;
    padding: 0.63rem 0.75rem;

    .action {
      font-size: 1.2rem;
      width: 2.5rem;
      height: 2.5rem;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      color: var(--gray-800);
      cursor: pointer;

      &:hover {
        background-color: var(--main-light-3);
      }
    }
    .his-title {
      font-size: 1rem;
      // color: #606266;
      font-weight: bold;
    }
  }

  .conversation-list {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    max-height: 100%;
  }

  .conversation-list .conversation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    width: 100%;
    user-select: none;
    transition: background-color 0.2s ease-in-out;

    &__title {
      color: var(--gray-700);
      white-space: nowrap; /* 禁止换行 */
      overflow: hidden;    /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 显示省略号 */
    }

    &__delete {
      // display: none;
      color: var(--gray-500);
      transition: all 0.2s ease-in-out;

      &:hover {
        color: #F93A37;
        background-color: #EEE;
      }
    }

    &.active {
      // border-right: 3px solid var(--main-500);
      padding-right: 13px;
      background: linear-gradient(315deg, #E8F5FF 0%, #FDF3FF 100%);;

      & .conversation__title {
        color: var(--gray-1000);
      }
    }

    &:not(.active):hover {
      background-color: var(--main-light-3);

      // & .conversation__delete {
      //   display: block;
      // }
    }

  }
}

.conversation-list::-webkit-scrollbar {
  position: absolute;
  width: 4px;
}

.conversation-list::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.conversation-list::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 4px;
}

.conversation-list::-webkit-scrollbar-thumb:hover {
  background: rgb(100, 100, 100);
  border-radius: 4px;
}

.conversation-list::-webkit-scrollbar-thumb:active {
  background: rgb(68, 68, 68);
  border-radius: 4px;
}
.conver-layer {
  display: none;
}
.close-icon {
  display: none;
}

@media (max-width: 720px) {
  .conver-layer {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.4);
    z-index: 100;
  }
  .conversations {
    position: absolute;
    z-index: 101;
    width: 75%;
    max-width: unset;
    height: 100%;
    overflow: auto;
    box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.05);
    .menu-icon {
      display: none;
    }
    .close-icon {
      display: block;
    }
  }
}
</style>
