import getRequestFunction from "rt-request";
import { message } from 'ant-design-vue'
import * as constant from "@/config/constant";
import { getLocalStorage, setLocalStorage, removeLocalStorage, getSessionStorage, setSessionStorage, removeSessionStorage } from "rt-common-functions";

export const request = getRequestFunction({
	getToken: () => reGetLocalStorage(constant.RT_SESSION_TOKEN),
	errorCallBack: (err) => {
		if(err !== '取消请求') {
			console.error(err);
			message.error('网络请求失败，请联系管理员');
		}
	},
});

// rt-common-functions封装的getLocalStorage是以object做处理,兼容旧项目处理
const reGetLocalStorage = (key) => {
	let res = window.localStorage.getItem(key);
	if (res && typeof JSON.parse(res) === "string") {
		return JSON.parse(res);
	}
	return getLocalStorage(key);
};
const reGetSessionStorage = (key) => {
	let res = window.sessionStorage.getItem(key);
	if (res && typeof JSON.parse(res) === "string") {
		return JSON.parse(res);
	}
	return getSessionStorage(key);
};

export const storageMap = {
	setSessionStorage,
	getSessionStorage: reGetSessionStorage,
	removeSessionStorage,
	setLocalStorage,
	getLocalStorage: reGetLocalStorage,
	removeLocalStorage,
};

export const generateRandomHash = (length) => {
  let chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let hash = '';
  for (let i = 0; i < length; i++) {
    hash += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return hash;
}

// 复制
export const copyToClipboard = (textToCopy) => {
	// navigator clipboard 需要https等安全上下文
	if (navigator.clipboard && window.isSecureContext) {
		// navigator clipboard 向剪贴板写文本
		message.success('文本已复制到剪贴板')
		return navigator.clipboard.writeText(textToCopy);
	} else {
		// 创建text area
		let textArea = document.createElement("textarea");
		textArea.value = textToCopy;
		// 使text area不在viewport，同时设置不可见
		textArea.style.position = "absolute";
		textArea.style.opacity = "0";
		textArea.style.left = "-999999px";
		textArea.style.top = "-999999px";
		document.body.appendChild(textArea);
		textArea.focus();
		textArea.select();
		return new Promise((res, rej) => {
			// 执行复制命令并移除文本框
			document.execCommand("copy") ? res(null) : rej();
			textArea.remove();
			message.success('文本已复制到剪贴板')
		});
	}
};