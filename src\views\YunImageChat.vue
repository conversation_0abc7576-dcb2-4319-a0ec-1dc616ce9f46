<template>
  <div class="yun-image-chat">
    <div class="chat-cont" ref="chatContainer">
      <div
        v-if="displayMessage"
        :key="displayMessage.id"
        class="message-box"
        :class="displayMessage.role"
        ref="messageBox"
      >
        <!-- 解读中 -->
        <div v-if="displayMessage.text?.length == 0 && displayMessage.status=='init'"  class="chat-cont-inner">
          <img src="@/assets/images/reading-icon.png" alt="">
        </div>
        <div v-else-if="displayMessage.reasoning_content" class="searching-msg">
          <a-collapse
            v-model:activeKey="displayMessage.showThinking"
            :bordered="false"
            style="background: rgb(255, 255, 255)"
            @change="toggleCollapse"
          >
            <template #expandIcon="{ isActive }">
              <caret-right-outlined :rotate="isActive ? 90 : 0" />
            </template>
            <a-collapse-panel
              key="show"
              :header="displayMessage.status=='reasoning' ? '正在思考...' : '推理过程'"
              :style="'background: #f7f7f7; border-radius: 8px; margin-bottom: 24px; border: 0; overflow: hidden'"
            >
              <Bubble
                :content="displayMessage.reasoning_content"
                :typing="!!displayMessage.splitByLetter && !displayMessage.noShowThinkBubble ? aiBubbleType : false"
                :onTypingComplete="finishReasponHandler(displayMessage)"
              ></Bubble>
              <!-- <p style="white-space: pre-line;word-break: break-word;">{{ displayMessage.reasoning_content }}</p> -->
            </a-collapse-panel>
          </a-collapse>
        </div>
        <div v-else-if="displayMessage.status == 'searching'" class="searching-msg"><i>正在检索……</i></div>
        <div v-else-if="displayMessage.status === 'generating'" class="searching-msg"><i>正在生成……</i></div>
        <div
          v-else-if="(displayMessage.text?.length == 0 && !['reasoning', 'pausing', 'finished', 'loading'].includes(displayMessage.status)) || displayMessage.status == 'error' || (!['finished', 'pausing', 'loading'].includes(displayMessage.status))"
          class="err-msg"
          @click="retryMessage(displayMessage.id)"
        >
          请求错误，请重试。
        </div>
        <div v-if="displayMessage.text && displayMessage.status !== 'error'"
          class="message-md"
          @click="consoleMsg(displayMessage)"
          :key="message.id"
        >
          <Bubble
            :content="displayMessage.text"
            :messageRender="bubbleRenderMessage"
            :typing="!!displayMessage.splitByLetter ? aiBubbleType : false"
            :onTypingComplete="finishReasponHandler(displayMessage)"
          >
          </Bubble>
        </div>
        <RefsComponent 
          v-if="displayMessage.role=='received' && displayMessage.status=='finished' && (!displayMessage.splitByLetter || displayMessage.isBubbleFlag)" 
          :message="displayMessage" 
        />
      </div>
      <!-- 解读中 -->
      <div v-else>
        <img src="@/assets/images/reading-icon.png" alt="">
      </div>
    </div>
    <div class="yun-footer">
      <img src="@/assets/images/deepseek-icon.png" alt="" style="margin-right:0.13rem">
      <span>内容由DeepSeek-R1生成,请以医生的诊断为准</span>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, computed, nextTick, onBeforeUnmount } from 'vue'
import { message } from 'ant-design-vue'
import { Bubble } from 'ant-design-x-vue';
import { CaretRightOutlined } from '@ant-design/icons-vue'
import RefsComponent from '@/components/RefsComponent.vue'
import {request, generateRandomHash} from '@/hooks/utils.js'
import { useMarkChatContent, useMessage } from '@/hooks/useChat.js'
import api from '@/config/api.js'

const { bubbleRenderMessage, aiBubbleType, heightObserver, toggleCollapse, resizeObserver} = useMarkChatContent();
const {findCurrentMessage, scrollToBottom, groupRefs, updateMessage} = useMessage();
const inputText = '报告解读';

// 只展示最新一条消息
const state = reactive({
  userInfo: JSON.parse(sessionStorage.getItem('ai_general_info') || '{}'),
})
const chatContainer = ref(null)
const messageBox = ref(null)
let curExamNo = state.userInfo?.busId;
let curNewConvName = curExamNo && (state.userInfo?.name || state.userInfo?.patLocalId) ? `${state.userInfo?.name || ''}(${state.userInfo?.patLocalId || ''})` : '';
const conv = ref({
  id: generateRandomHash(8),
  title: curNewConvName || '新对话',
  history: [],
  messages: [],
  inputText: inputText,
  examNo: curExamNo || ''
})

const meta = reactive(JSON.parse(localStorage.getItem('meta')) || {
  use_graph: false,
  use_web: false,
  graph_name: "neo4j",
  selectedKB: null,
  stream: true,
  summary_title: false,
  history_round: 5,
  db_name: null,
})

const displayMessage = computed(() => {
  if(conv.value?.messages?.length && conv.value.messages[conv.value.messages.length - 1]?.role === 'received') {
    nextTick(() => {
      if(messageBox.value) {
        heightObserver(messageBox.value, 0, () => {
          scrollToBottom(chatContainer);
        });
      }
    })
    return conv.value.messages[conv.value.messages.length - 1];
  }
  return null;
});
// 获取历史列表
const getHistoryList = async () => {
  let params = {
    userId: state.userInfo?.userId,
    busType: state.userInfo?.busType,
  };
  let res = await request(api.historyList, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '操作失败');
    return;
  }
  let data = res.result || [];
  if(data?.length) {
    data[0].messages = [];
    data[0].history = [];
    data[0].id = data[0].chatId;
    conv.value = data[0];
    // 获取详情，回显上次继续需要(暂无用到)
    // await getChatDetail(data[0].chatId);
  }
}

// 获取对话详情
const getChatDetail = async(chatId) => {
  if(!chatId) {
    message.error('获取对话失败，不存在id');
    return;
  }
  let params = {
    id: chatId,
    userId: state.userInfo?.userId,
    busType: state.userInfo?.busType,
    pageNo: 0,
    pageSize: 1,
  };
  let res = await request(api.historyInfo, 'post', params);
  if (!res || res.status !== '0') {
    message.error(res?.message || '操作失败');
    return;
  }
  let data = res.result || [];
  let reverseData = data.reverse(); //数组倒着显示
  // 最新一条AI回复消息
  if(reverseData[0]?.role === 'received' && (reverseData[0]?.text || reverseData[0]?.reasoning_content)) {
    conv.value.messages = [reverseData[0]];
    conv.value.history = [reverseData[0]];
  }
}
const consoleMsg = (msg) => console.log(msg)
// 发送新消息
const sendMessage = () => {
  let currentConv = conv.value;
  const sendExamNo = currentConv?.examNo || '';
  appendUserMessage(inputText);
  appendAiMessage("", null, '', sendExamNo);
  const cur_res_id = currentConv.messages?.length ? currentConv.messages[currentConv.messages.length - 1].id : '';
  fetchChatResponse(inputText, cur_res_id, currentConv.id);
}

// 新函数用于处理 fetch 请求
const fetchChatResponse = async(user_input, cur_res_id, convId) => {
  let curConvId = convId || conv.value.id;
  const sendExamNo = conv.value?.examNo || '';
  let {userId, busType, examNo, description, impression, sampleSeen, examParam, recommendation} = state.userInfo || {};
  let params = {
    query: user_input,
    history: conv.value.history,
    examNo: sendExamNo,
    cur_res_id: curConvId,
    userId,
    busType,
    description,
    impression,
    sampleSeen,
    examParam,
    recommendation
  };
  fetch(api.sendQuestion, {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then((response) => {
    if (!response.body) throw new Error("ReadableStream not supported.");
    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = '';

    const readChunk = () => {
      return reader.read().then(({ done, value }) => {
        if (done) {
          groupRefs(cur_res_id, [conv.value], chatContainer);
          updateMessage({showThinking: "no", id: cur_res_id}, conv, [conv.value], chatContainer);
          return;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');

        // 处理除最后一行外的所有完整行
        for (let i = 0; i < lines.length - 1; i++) {
          const line = lines[i].trim();
          if (line) {
            try {
              const data = JSON.parse(line);
              updateMessage({
                id: cur_res_id,
                text: data.response,
                reasoning_content: data.reasoning_content,
                status: data.status,
                meta: data.meta,
                ...data,
                splitByLetter: true
              }, conv, [conv.value], chatContainer);

              if (data.history) {
                conv.value.history = data.history;
              }
            } catch (e) {
              console.error('JSON 解析错误:', e, line);
            }
          }
        }

        // 保留最后一个可能不完整的行
        buffer = lines[lines.length - 1];

        return readChunk(); // 继续读取
      }).catch((error) => {
        console.error('读取错误：', error);
      });
    };
    readChunk();
  })
  .catch((error) => {
    if(error === '取消请求') {
      console.log('取消请求：',error);
    } else {
      console.error('请求报错：', error);
      updateMessage({
        id: cur_res_id,
        status: "error",
      }, conv, [conv.value], chatContainer);
    }
  });
}
// 缓存用户发送消息
const appendUserMessage = (msg) => {
  let curConv = conv.value;
  curConv?.messages?.push({
    id: generateRandomHash(16),
    role: 'sent',
    text: msg,
    createTime: Date.now()
  })
}
// 新增AI消息
const appendAiMessage = (text, refs=null, status, examNo) => {
  let curConv = conv.value;
  curConv?.messages?.push({
    id: generateRandomHash(16),
    role: 'received',
    text: text,
    reasoning_content: '',
    refs,
    status: status || "init",
    meta: {},
    showThinking: "show"
  })
  scrollToBottom(chatContainer)
}

const finishReasponHandler = (displayMessage) => {
  if(displayMessage.status === 'finished' && !displayMessage.isBubbleFlag) {
    setTimeout(() => {
      displayMessage.isBubbleFlag = true;
      displayMessage.splitByLetter = false;
    }, 2000)
  }
}

const pageInit = async() => {
  await getHistoryList();  //获取历史拿到chatId
  // 发送新消息
  sendMessage();
}


// 重连
const retryMessage = (id) => {
  // 找到 id 对应的 message，然后删除包含 message 在内以及后面所有的 message
  const index = conv.value.messages.findIndex(msg => msg.id === id);
  const pastMessage = conv.value.messages[index-1]
  if(!pastMessage) {
    return;
  }
  conv.value.inputText = pastMessage.text
  // if (index !== -1) {
  //   conv.value.messages = conv.value.messages.slice(0, index-1);
  // }
  sendMessage();
}
onMounted(() => {
  pageInit();
})

onBeforeUnmount(() => {
  if(resizeObserver) {
    resizeObserver.disconnect();
  }
});
</script>

<style lang="less">
.yun-image-chat {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #FFF;
  .ant-bubble-content {
    background-color: unset;
    border-radius: 0;
    padding: 0;
    white-space: pre-line;
    word-break: break-word;
    min-height: 0 !important;
    * {
      line-height: 1.2rem;
    }
  }
  .chat-cont {
    flex: 1;
    padding: 0.75rem;
    overflow: auto;
    .err-msg {
      color: #eb8080;
      border: 1px solid #eb8080;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      text-align: left;
      background: #FFF5F5;
      margin-bottom: 10px;
      cursor: pointer;
    }
    .message-box {
      pre {
        border-radius: 8px;
        font-size: 14px;
        border: 1px solid var(--main-light-3);
        padding: 1rem;

        &:has(code.hljs) {
          padding: 0;
        }
      }
      .received-box {
        background: #FFFFFF;
        box-shadow: 0rem 0rem 0.25rem 0rem rgba(0,0,0,0.05);
        border-radius: 0.25rem 0.75rem 0.75rem 0.75rem;
        border: 0.06rem solid #DCDFE6;
        padding: 0.5rem 0.75rem;
        .received-tip, .received-qus {
          font-size: 1rem;
          color: #000000;
          line-height: 1.75rem;
          text-align: left;
        }
        .received-qus, .received-example {
          margin-top: 0.5rem;
        }
        .example {
          cursor: pointer;
          color: #002BFF;
          font-size: 1rem;
          line-height: 1.75rem;
          text-decoration: underline;
          &.disabled {
            cursor: not-allowed;
            pointer-events: none;
          }
          &:hover {
            opacity: 0.8;
          }
          & + .example {
            margin-left: 0.5rem;
          }
        }
      }
    }
    .message-md {
      word-wrap: break-word;
      // white-space: pre-line;
      h1, h2, h3, h4, h5, h6 {
        font-size: 1rem;
      }
      p {
        margin: 0;
      }
      li, ol, ul {
        margin: 0;
        padding-top: 0;
        padding-bottom: 0;
        & > p {
          margin: 0.25rem 0;
        }
      }
      // ol, ul {
      // }
      table {
        border-collapse: collapse;
        th, td {
          border: 1px solid #9a9a9a;
          padding: 4px;
        }
      }
      h1,h2,h3,h4,h5 {
        margin-top: 0!important;
        margin-bottom: 0!important;
      }
    }
  }
  .yun-footer {
    position: sticky;
    bottom: 0;
    padding: 0.63rem 0;
    font-size: 0.75rem;
    color: #191919;
    border-top: 1px solid #E3E3E3;
    background: #FFF;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 1.75rem;
    }
  }
}
</style>