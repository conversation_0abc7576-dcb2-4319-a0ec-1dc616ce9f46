import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import AutoImport from 'unplugin-auto-import/vite'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    base: './',
    plugins: [
      vue(),
      // 自动导入 Ant Design Vue 组件
      Components({
        resolvers: [AntDesignVueResolver({ importStyle: 'less' })],
      }),
      // 自动导入 API（可选）
      AutoImport({
        resolvers: [AntDesignVueResolver()],
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    build: {
      outDir: './dist/webpacs-ai-assistant',
      cssCodeSplit: false,  // 禁用 CSS 拆分
      terserOptions: {
        compress: {
          drop_console: false, // 移除 console.log
          drop_debugger: true, // 移除 debugger
        },
        output: {
          comments: false, // 删除注释
        },
      },
    },
    server: {
      port: 9098,
      proxy: {
        '/api': {
          target: 'http://*************:5173',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/ClinicView': {
          target: 'http://*************:9090',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/cloudpacsApi': {
          // target: 'http://*************',
          target: 'http://*************',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/api/, '')
        }
      },
      watch: {
        usePolling: true,
        ignored: ['**/node_modules/**', '**/dist/**'],
      },
      host: '0.0.0.0',
    }
  }
})
