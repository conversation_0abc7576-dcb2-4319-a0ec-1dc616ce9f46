<template>
  <div class="know-source-wrap" v-if="docInfoList?.length">
    <div class="source-tit">知识来源</div>
    <ul class="source-doc-list" v-for="(item, index) in docInfoList" :key="item.resourceId">
      <li 
        class="source-doc-item"
        @click="previewDoc(item)"
      >{{item.resourceTitle}}
      </li>
    </ul>
  </div>
  <KnowArticlePreview
    :showDialog.sync="showArticleDialog"
    :docUrl="docUrl"
    @update:showDialog="(newVal) => showArticleDialog = newVal"
  ></KnowArticlePreview>
</template>

<script setup>
import { ref, reactive } from 'vue';
import KnowArticlePreview from '@/components/KnowArticlePreview.vue';
import { getDocUrl } from '@/hooks/useChat.js'
const props = defineProps({
  docInfoList: {
    type: Array,
    default: () => []
  }
});

const showArticleDialog = ref(false);
const docUrl = ref('');
let currentDoc = reactive({});

// 查看知识来源
const previewDoc = async(item) => {
  currentDoc = item;
  let params = {
    docId: currentDoc.resourceId
  };
  let url = await getDocUrl(params);
  if(url) {
    showArticleDialog.value = true;
    docUrl.value = url;
    // window.open(url);
  }
}
</script>

<style scoped lang="less">
.know-source-wrap {
  margin-top: 1.5rem;
  .source-tit {
    color: #666;
    font-size: 0.85rem;
  }
  .source-doc-list {
    padding-left: 1rem;
    .source-doc-item {
      font-size: 0.88rem;
      color: #002BFF;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
  }
}
</style>
