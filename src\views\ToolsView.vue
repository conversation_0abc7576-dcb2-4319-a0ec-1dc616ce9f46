<template>
  <div class="tools-container layout-container">
    <HeaderComponent
      title="工具箱"
      description="这里展示了各种可用的工具。"
    >
    </HeaderComponent>

    <div class="tools-grid">
      <div v-for="tool in tools" :key="tool.id" class="tool-card" @click="navigateToTool(tool.name)">
        <div class="tool-header">
          <!-- <div class="tool-icon">
            <component :is="iconMap[tool.name]" />
          </div> -->
          <h3>{{ tool.title }}</h3>
        </div>
        <div class="tool-info">
          <p>{{ tool.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { CalculatorOutlined, FileSearchOutlined, TranslationOutlined } from '@ant-design/icons-vue';
import HeaderComponent from '@/components/HeaderComponent.vue';

const router = useRouter();
const tools = ref([]);
const iconMap = ref({
  "text-chunking": FileSearchOutlined
})

const state = reactive({
  loadingTools: true,
})

const getTools = () => {
  state.loadingTools = true
  fetch('/api/tool/')
    .then(response => response.json())
    .then(data => {
      tools.value = data;
      state.loadingTools = false;
    })
    .catch(error => {
      console.error('Error fetching tools:', error);
      state.loadingTools = false;
    });
};

const navigateToTool = (toolName) => {
  router.push({name: toolName});
};

onMounted(() => {
  getTools();
});
</script>

<style scoped lang="less">
.tools-container {
  padding: 0;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  padding: 20px;

  .tool-card {
    display: flex;
    flex-direction: column;
    background-color: white;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    padding: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .tool-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      font-size: 1rem;

      .tool-icon {
        margin-right: 10px;
      }

      h3 {
        margin: 0;
      }
    }

    .tool-info {
      flex-grow: 1;

      p {
        margin: 0;
        color: var(--gray-800);
      }
    }
  }
}
</style>
