// 与pacs相关交互
import { message } from 'ant-design-vue';

// 通知pacs方法
window.aiSendToPacs = (params, successCallBack, errorCallBack) => {
  successCallBack && successCallBack();
  let paramsJson = JSON.stringify(params);
	if(!window.ReturnCallJsResult) {
    // message.error('未找到ReturnCallJsResult方法');
    // errorCallBack && errorCallBack({message: '未找到ReturnCallJsResult方法'});
    console.log('ReturnCallJsResult 方法不存在,通过postMessage(srOuterOptListener)回传结果：', paramsJson);
    window.parent.postMessage({
      message: 'srOuterOptListener',
      data: params
    }, '*');
  } else {
    console.log('ReturnCallJsResult入参：', paramsJson);
    window.ReturnCallJsResult(paramsJson);
  }
};

/**
 * @method srCommonOuterOpt
 * @param {Object} params {type,status,message,result,param}json
 * @desc 外部调用公共方法
 */
window.srCommonOuterOpt = function(params, callBack) {
  try {
    console.log(`执行srCommonOuterOpt,公共方法入参${Date.now()}--->`, params);
    if(params && typeof params === 'string') {
      params = JSON.parse(params);
    }
    window.srCommonOuterOptHandler && srCommonOuterOptHandler(params, callBack);  //报告内的方法
  } catch(e) {
    console.error('数据格式错误：'+ e);
  }
}
